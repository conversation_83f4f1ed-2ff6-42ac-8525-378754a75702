import { z } from 'zod';

import { urlValidator } from '../utils';

export const getStoreLocatorOrganizationConfigurationValidator = z.object({
    cloudfrontDistributionId: z.string(),
    organizationName: z.string(),
    baseUrl: z.string(),
    tailwindConfig: z.string(),
    tailwindClassesMap: z.string(),
    favIconUrl: urlValidator(),
});

export type GetStoreLocatorOrganizationConfigurationDto = z.infer<typeof getStoreLocatorOrganizationConfigurationValidator>;
