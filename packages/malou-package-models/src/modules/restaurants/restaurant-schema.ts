import {
    ApplicationLanguage,
    BusinessCategory,
    checkSocialNetworkUrls,
    cleanUrl,
    containsForbiddenDomains,
    CountryCode,
    DAYS,
    descriptionSize,
    emailRegex,
    formatSocialNetworkUrls,
    GMapsApiVersion,
    isValidUrl,
    PlatformAccessStatus,
    PlatformAccessType,
    PlatformDataFetchedStatus,
    PlatformKey,
    PostType,
    RestaurantCalendarEventsCountry,
    SocialNetworkKey,
    urlRegex,
} from '@malou-io/package-utils';

import { JSONSchemaExtraProps } from ':core/mongoose-json-schema/definitions/types';

export const restaurantJSONSchema = {
    $schema: 'http://json-schema.org/draft-06/schema#',
    title: 'Restaurant',
    type: 'object',
    additionalProperties: false,
    properties: {
        _id: {
            type: 'string',
            format: 'objectId',
        },
        placeId: {
            description: `
                Usually a Google Maps Place ID, but it can also be a Facebook ID in some cases (brands).

                There’s no unique index on this field but in general Place IDs are unique among active
                restaurants. However, it is frequent that many inactive restaurants share the same Place ID.`,
            type: 'string',
        },
        uniqueKey: {
            type: 'string',
        },
        access: {
            type: 'array',
            items: {
                $ref: '#/definitions/Access',
            },
            default: [],
        },
        active: {
            type: 'boolean',
            default: true,
        },
        address: {
            $ref: '#/definitions/Address',
            nullable: true,
            default: null,
        },
        keywordToolApiLocationId: {
            type: 'string',
            nullable: true,
            default: null,
        },
        availableHoursTypeIds: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
            },
            default: [],
        },
        bookmarkedPosts: {
            type: 'array',
            items: {
                $ref: '#/definitions/BookmarkedPost',
            },
        },
        bricks: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
                ref: 'Brick',
            },
        },
        calendarEvents: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
                ref: 'CalendarEvent',
            },
        },
        category: {
            type: 'string',
            format: 'objectId',
            ref: 'Category',
        },
        categoryList: {
            type: 'array',
            items: {
                type: 'string',
                format: 'objectId',
                ref: 'Category',
            },
            default: [],
        },
        coverChanged: {
            type: 'boolean',
            default: false,
        },
        createdAt: {
            type: 'string',
            format: 'date-time',
        },
        descriptions: {
            type: 'array',
            items: {
                $ref: '#/definitions/Description',
            },
            default: [],
            // TODO: uncomment when the restaurant every restaurant description are stable
            // validate: {
            //    validator(v) {
            //        return checkDescriptions(v);
            //    },
            //    message: (props) => `Descriptions should be valid, value: ${props.value}`,
            // },
        },
        isClosedTemporarily: {
            type: 'boolean',
            default: false,
        },
        logoChanged: {
            type: 'boolean',
            default: false,
        },
        menuUrl: {
            type: 'string',
            format: 'uri',
            nullable: true,
            validate: {
                validator(v) {
                    return v === null || isValidUrl(v, { allowEmpty: true });
                },
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
            description: 'Menu URL, undefined at first. Empty string if a menuUrl was set then removed',
        },
        reservationUrl: {
            type: 'string',
            description: 'Always nullish on brand accounts. Undefined at first. Empty string if an reservationUrl was set then removed',
            format: 'uri',
            validate: {
                validator(v) {
                    return v === null || (isValidUrl(v, { allowEmpty: true }) && !containsForbiddenDomains(v, ['google.com']));
                },
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
            nullable: true,
        },
        orderUrl: {
            type: 'string',
            description: 'Always nullish on brand accounts. Undefined at first. Empty string if an orderUrl was set then removed',
            format: 'uri',
            validate: {
                validator(v) {
                    return v === null || isValidUrl(v, { allowEmpty: true });
                },
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
            nullable: true,
        },
        socialNetworkUrls: {
            type: 'array',
            items: {
                type: 'object',
                additionalProperties: false,
                properties: {
                    key: {
                        enum: Object.values(SocialNetworkKey),
                    },
                    url: {
                        type: 'string',
                        format: 'uri',
                    },
                },
                required: ['key', 'url'],
            },
            validate: {
                validator: checkSocialNetworkUrls,
                message: (props) => `Social network urls should be valid, value: ${props.value}`,
            },
            set(socialNetworkUrls) {
                return formatSocialNetworkUrls(socialNetworkUrls as { key: SocialNetworkKey; url: string }[]);
            },
            default: [],
        },
        name: {
            type: 'string',
        },
        openingDate: {
            type: 'string',
            format: 'date-time',
            nullable: true,
            default: null,
        },
        otherHours: {
            type: 'array',
            items: {
                $ref: '#/definitions/OtherHour',
            },
        },
        phone: {
            $ref: '#/definitions/Phone',
            nullable: true,
        },
        appleBusinessConnect: {
            $ref: '#/definitions/AppleBusinessConnect',
        },
        regularHours: {
            type: 'array',
            nullable: true,
            items: {
                $ref: '#/definitions/RegularHour',
            },
        },
        relatedUrls: {
            type: 'array',
            items: {
                type: 'string',
            },
        },
        specialHours: {
            type: 'array',
            items: {
                $ref: '#/definitions/SpecialHour',
            },
        },
        type: {
            enum: Object.values(BusinessCategory),
            default: BusinessCategory.LOCAL_BUSINESS,
        },
        updatedAt: {
            type: 'string',
            format: 'date-time',
        },
        organizationId: {
            type: 'string',
            format: 'objectId',
            ref: 'Organization',
        },
        ai: {
            $ref: '#/definitions/AI',
            default: { monthlyCallCount: 0, callCount: 0 },
        },
        boosterPack: {
            $ref: '#/definitions/BoosterPack',
            default: { activated: false, activationDate: null },
        },
        gmbEntities: {
            type: 'object',
            additionalProperties: false,
            description: 'GMB linked entity ids',
            properties: {
                orderUrl: {
                    type: 'string',
                    nullable: true,
                },
                reservationUrl: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: [],
        },
        roiActivated: {
            type: 'boolean',
            default: false,
        },
        currentState: {
            $ref: '#/definitions/CurrentState',
        },
        reviewsLastUpdate: {
            type: 'string',
            format: 'date-time',
        },
        postsLastUpdate: {
            description: 'When a partial synchronization of recent posts only has been done. See also allPostsLastSync.',
            type: 'string',
            format: 'date-time',
        },
        allPostsLastSync: {
            description: `
                When a full synchronization of all the posts only has been done. See also postsLastUpdate.

                This field is initialized by the route GET /:platform_key/restaurants/:restaurant_id/pull when a
                platform is added.`,
            type: 'string',
            format: 'date-time',
        },
        latlng: {
            $ref: '#/definitions/Latlng',
        },
        cover: {
            type: 'string',
            format: 'objectId',
            ref: 'Media',
        },
        logo: {
            type: 'string',
            format: 'objectId',
            ref: 'Media',
        },
        website: {
            type: 'string',
            format: 'uri',
            validate: {
                validator(v) {
                    return v === null || isValidUrl(v, { allowEmpty: true });
                },
                message: (props) => `Url should be valid, value: ${props.value}`,
            },
            set: cleanUrl,
            nullable: true,
            description: 'Website, undefined at first. Empty string if a website was set then removed',
        },
        bricksPostalCode: {
            type: 'string',
        },
        calendarEventsCountry: {
            enum: Object.values(RestaurantCalendarEventsCountry),
        },
        commentsLastUpdate: {
            type: 'string',
            format: 'date-time',
        },
        socialId: {
            type: 'string',
        },
        email: {
            type: 'string',
            nullable: true,
            match: emailRegex,
        },
        internalName: {
            type: 'string',
        },
        isClaimed: {
            type: 'boolean',
        },
        rating: {
            type: 'number',
            nullable: true,
        },
        completionScore: {
            type: 'number',
            description: 'A score between 0 and 100 that indicates how complete the restaurant profile is',
            nullable: true,
        },
        toolboxMenuUrl: {
            type: 'string',
        },
        minimalReviewsDateForReviewReplyAutomation: {
            type: 'string',
            format: 'date-time',
        },
        isYextActivated: {
            type: 'boolean',
            default: false,
        },
        totemDisplayName: {
            // set manually in the db
            $ref: '#/definitions/TotemDisplayName',
        },
        gMapsApiVersion: {
            type: 'string',
            enum: Object.values(GMapsApiVersion),
            default: GMapsApiVersion.V2,
        },
        brandKeywords: {
            $ref: '#/definitions/BrandKeywords',
        },
    },
    required: [
        '_id',
        'access',
        'active',
        'ai',
        'bookmarkedPosts',
        'boosterPack',
        'bricks',
        'calendarEvents',
        'categoryList',
        'coverChanged',
        'createdAt',
        'descriptions',
        'logoChanged',
        'name',
        'organizationId',
        'placeId',
        'relatedUrls',
        'specialHours',
        'type',
        'isClosedTemporarily',
        'uniqueKey',
        'updatedAt',
    ],
    definitions: {
        Access: {
            type: 'object',
            additionalProperties: false,
            properties: {
                platformKey: {
                    enum: Object.values(PlatformKey),
                },
                status: {
                    enum: Object.values(PlatformAccessStatus),
                },
                accessType: {
                    enum: Object.values(PlatformAccessType),
                },
                lastUpdated: {
                    type: 'string',
                    format: 'date-time',
                },
                lastVerified: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                    default: null,
                },
                active: {
                    type: 'boolean',
                    default: false,
                },
                data: {
                    $ref: '#/definitions/Data',
                    nullable: true,
                },
            },
            required: ['accessType', 'active', 'lastUpdated', 'platformKey', 'status'],
            title: 'Access',
        },
        Data: {
            type: 'object',
            additionalProperties: false,
            properties: {
                login: {
                    type: 'string',
                },
                password: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: ['login'],
            title: 'Data',
        },
        Address: {
            type: 'object',
            additionalProperties: false,
            properties: {
                streetNumber: {
                    type: 'string',
                },
                route: {
                    type: 'string',
                },
                locality: {
                    type: 'string',
                },
                regionCode: {
                    enum: Object.values(CountryCode),
                },
                country: {
                    type: 'string',
                },
                postalCode: {
                    type: 'string',
                },
                formattedAddress: {
                    type: 'string',
                },
                administrativeArea: {
                    type: 'string',
                },
            },
            required: ['country', 'regionCode'],
            title: 'Address',
        },
        AI: {
            type: 'object',
            additionalProperties: false,
            properties: {
                activationDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                },
                callCount: {
                    type: 'integer',
                },
                monthlyCallCount: {
                    type: 'integer',
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['monthlyCallCount'],
            title: 'AI',
        },
        BookmarkedPost: {
            type: 'object',
            additionalProperties: false,
            properties: {
                socialId: {
                    type: 'string',
                },
                key: {
                    enum: [PlatformKey.INSTAGRAM],
                },
                username: {
                    type: 'string',
                },
                permalink: {
                    type: 'string',
                },
                caption: {
                    type: 'string',
                    nullable: true,
                },
                comments: {
                    type: 'integer',
                },
                likes: {
                    type: 'integer',
                },
                url: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                    default: null,
                },
                thumbnail: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                    default: null,
                },
                createdAt: {
                    type: 'string',
                    format: 'date-time',
                },
                type: {
                    type: 'string',
                    nullable: true,
                },
                backupUrl: {
                    type: 'string',
                    format: 'uri',
                    match: urlRegex,
                    nullable: true,
                },
                nbFollowers: {
                    type: 'integer',
                    nullable: true,
                },
                accountEngagement: {
                    type: 'number',
                    nullable: true,
                },
                hoveredPosts: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/HoveredPost',
                    },
                },
                commentsWithoutOwnerComments: {
                    type: 'integer',
                },
                replies: {
                    type: 'integer',
                },
                repliesWithoutOwnerReplies: {
                    type: 'integer',
                },
                impressions: {
                    type: 'integer',
                },
                postType: {
                    type: 'string',
                },
                showOwnerEntities: {
                    type: 'boolean',
                },
                carouselUrls: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/CarouselURL',
                    },
                },
                backupCarouselUrls: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/CarouselURL',
                    },
                },
                engagementRate: {
                    type: 'number',
                },
                plays: {
                    type: 'integer',
                },
            },
            required: [
                'backupCarouselUrls',
                'carouselUrls',
                'comments',
                'createdAt',
                'hoveredPosts',
                'key',
                'likes',
                'permalink',
                'socialId',
                'username',
            ],
            title: 'BookmarkedPost',
        },
        BoosterPack: {
            type: 'object',
            additionalProperties: false,
            properties: {
                activated: {
                    type: 'boolean',
                },
                activationDate: {
                    anyOf: [
                        {
                            type: 'string',
                            format: 'date-time',
                        },
                        {
                            type: 'null',
                        },
                    ],
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['activated'],
            title: 'BoosterPack',
        },
        CarouselURL: {
            type: 'object',
            additionalProperties: false,
            properties: {
                type: {
                    enum: Object.values(PostType),
                },
                url: {
                    type: 'string',
                },
                _id: {
                    type: 'string',
                },
            },
            required: ['type', 'url'],
            title: 'CarouselURL',
        },
        HoveredPost: {
            type: 'object',
            additionalProperties: false,
            properties: {
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                url: {
                    type: 'string',
                },
            },
            required: [],
            title: 'HoveredPost',
        },
        File: {
            type: 'object',
            additionalProperties: false,
            title: 'File',
        },
        CurrentState: {
            type: 'object',
            additionalProperties: false,
            properties: {
                reviews: {
                    $ref: '#/definitions/Reviews',
                },
                posts: {
                    $ref: '#/definitions/Posts',
                },
                comments: {
                    $ref: '#/definitions/Comments',
                },
                messages: {
                    $ref: '#/definitions/Messages',
                },
            },
            required: ['reviews'],
            title: 'CurrentState',
        },
        Comments: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fetched: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        facebook: {
                            $ref: '#/definitions/Fetched',
                        },
                        instagram: {
                            $ref: '#/definitions/Fetched',
                        },
                    },
                },
            },
            required: ['fetched'],
            title: 'Comments',
        },
        Messages: {
            type: 'object',
            additionalProperties: false,
            properties: {
                lastSync: {
                    type: 'string',
                    format: 'date-time',
                },
                fetched: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        facebook: {
                            $ref: '#/definitions/Fetched',
                        },
                        instagram: {
                            $ref: '#/definitions/Fetched',
                        },
                    },
                },
            },
            required: ['fetched'],
            title: 'Messages',
        },
        Posts: {
            type: 'object',
            additionalProperties: false,
            properties: {
                lastSync: {
                    type: 'string',
                    format: 'date-time',
                },
            },
            required: ['lastSync'],
            title: 'Posts',
        },
        Reviews: {
            type: 'object',
            additionalProperties: false,
            properties: {
                fetched: {
                    type: 'object',
                    additionalProperties: false,
                    properties: {
                        gmb: {
                            $ref: '#/definitions/Fetched',
                        },
                        facebook: {
                            $ref: '#/definitions/Fetched',
                        },
                        foursquare: {
                            $ref: '#/definitions/Fetched',
                        },
                        tripadvisor: {
                            $ref: '#/definitions/Fetched',
                        },
                        yelp: {
                            $ref: '#/definitions/Fetched',
                        },
                        ubereats: {
                            $ref: '#/definitions/Fetched',
                        },
                        lafourchette: {
                            $ref: '#/definitions/Fetched',
                        },
                        deliveroo: {
                            $ref: '#/definitions/Fetched',
                        },
                        pagesjaunes: {
                            $ref: '#/definitions/Fetched',
                        },
                        zenchef: {
                            $ref: '#/definitions/Fetched',
                        },
                        opentable: {
                            $ref: '#/definitions/Fetched',
                        },
                        resy: {
                            $ref: '#/definitions/Fetched',
                        },
                    },
                },
            },
            required: ['fetched'],
            title: 'Reviews',
        },
        Fetched: {
            type: 'object',
            additionalProperties: false,
            properties: {
                status: {
                    enum: [...Object.values(PlatformDataFetchedStatus)],
                },
                lastTried: {
                    type: 'string',
                    format: 'date-time',
                    nullable: true,
                },
                error: {
                    type: 'string',
                    nullable: true,
                },
            },
            required: ['status'],
            title: 'Fetched',
        },
        Description: {
            type: 'object',
            additionalProperties: false,
            properties: {
                language: {
                    enum: [null, ...Object.values(ApplicationLanguage)],
                    nullable: true,
                },
                size: {
                    enum: [descriptionSize.LONG.key, descriptionSize.SHORT.key],
                },
                text: {
                    type: 'string',
                    nullable: true,
                    trim: true,
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
                createdAt: {
                    type: 'string',
                    format: 'date-time',
                },
                updatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
                keywordAnalysis: {
                    $ref: '#/definitions/KeywordAnalysis',
                },
                active: {
                    type: 'boolean',
                },
                platformKey: {
                    enum: Object.values(PlatformKey),
                },
                duplicatedFromRestaurantId: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['_id', 'createdAt', 'updatedAt', 'size'],
            title: 'Description',
        },
        KeywordAnalysis: {
            type: 'object',
            additionalProperties: false,
            properties: {
                keywords: {
                    type: 'array',
                    items: {
                        type: 'string',
                    },
                    default: [],
                },
                score: {
                    type: 'number',
                },
                count: {
                    type: 'integer',
                },
            },
            required: ['keywords'],
            title: 'KeywordAnalysis',
        },
        Latlng: {
            type: 'object',
            additionalProperties: false,
            properties: {
                lat: {
                    type: 'number',
                },
                lng: {
                    type: 'number',
                },
            },
            required: ['lat', 'lng'],
            title: 'Latlng',
        },
        Menu: {
            type: 'object',
            additionalProperties: false,
            properties: {
                sections: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Section',
                    },
                },
                socialId: {
                    type: 'string',
                },
                _id: {
                    type: 'string',
                },
                updatedAt: {
                    type: 'string',
                    format: 'date-time',
                },
                createdAt: {
                    type: 'string',
                    format: 'date-time',
                },
            },
            required: ['_id', 'createdAt', 'sections', 'socialId', 'updatedAt'],
            title: 'Menu',
        },
        Section: {
            type: 'object',
            additionalProperties: false,
            properties: {
                label: {
                    type: 'string',
                },
                socialId: {
                    type: 'string',
                },
                items: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/Item',
                    },
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['_id', 'items', 'label', 'socialId'],
            title: 'Section',
        },
        Item: {
            type: 'object',
            additionalProperties: false,
            properties: {
                label: {
                    type: 'string',
                },
                description: {
                    type: 'string',
                },
                socialId: {
                    type: 'string',
                },
                price: {
                    $ref: '#/definitions/Price',
                },
                _id: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['_id', 'label', 'socialId'],
            title: 'Item',
        },
        Price: {
            type: 'object',
            additionalProperties: false,
            properties: {
                currencyCode: {
                    type: 'string',
                },
                units: {
                    type: 'integer',
                },
            },
            required: ['currencyCode'],
            title: 'Price',
        },
        OtherHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                hoursTypeId: {
                    type: 'string',
                },
                periods: {
                    type: 'array',
                    items: {
                        $ref: '#/definitions/RegularHour',
                    },
                },
            },
            required: ['hoursTypeId', 'periods'],
            title: 'OtherHour',
        },
        Phone: {
            type: 'object',
            additionalProperties: false,
            properties: {
                prefix: {
                    type: 'integer',
                    nullable: true,
                },
                digits: {
                    type: 'integer',
                    nullable: true,
                },
            },
            required: [],
            title: 'Phone',
        },
        AppleBusinessConnect: {
            type: 'object',
            additionalProperties: false,
            properties: {
                locationId: {
                    type: 'string',
                    description: 'AppleBusinessConnect location ID. Refers to Location field "id" in the Apple API',
                },
                malouLocationId: {
                    type: 'string',
                    description: 'Malou custom Apple ID for this restaurant. Refers to Location field "partnerLocationId" in the Apple API',
                },
            },
            required: ['locationId', 'malouLocationId'],
            title: 'AppleBusinessConnect',
        },
        RegularHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                openDay: {
                    enum: Object.values(DAYS),
                },
                openTime: {
                    type: 'string',
                    match: /^(0[0-9]|1[0-9]|2[0-4]):[0-5][0-9]$/,
                },
                closeDay: {
                    enum: Object.values(DAYS),
                },
                closeTime: {
                    type: 'string',
                    match: /^(0[0-9]|1[0-9]|2[0-4]):[0-5][0-9]$/,
                },
                isClosed: {
                    type: 'boolean',
                    default: true,
                },
            },
            required: ['closeDay', 'isClosed', 'openDay'],
            title: 'RegularHour',
        },
        SpecialHour: {
            type: 'object',
            additionalProperties: false,
            properties: {
                name: {
                    type: 'string',
                },
                startDate: {
                    $ref: '#/definitions/Date',
                },
                openTime: {
                    type: 'string',
                    nullable: true,
                    default: null,
                    match: /^(0[0-9]|1[0-9]|2[0-4]):[0-5][0-9]$/,
                },
                endDate: {
                    $ref: '#/definitions/Date',
                },
                closeTime: {
                    type: 'string',
                    nullable: true,
                    default: null,
                    match: /^(0[0-9]|1[0-9]|2[0-4]):[0-5][0-9]$/,
                },
                isClosed: {
                    type: 'boolean',
                    default: false,
                },
                isFromCalendarEvent: {
                    type: 'boolean',
                    default: false,
                },
                duplicatedFromRestaurantId: {
                    type: 'string',
                    format: 'objectId',
                },
            },
            required: ['isClosed', 'startDate', 'endDate'],
            title: 'SpecialHour',
        },
        Date: {
            type: 'object',
            additionalProperties: false,
            properties: {
                day: {
                    type: 'integer',
                },
                year: {
                    type: 'integer',
                },
                month: {
                    type: 'integer',
                },
            },
            required: ['day', 'month', 'year'],
            title: 'Date',
        },
        TotemDisplayName: {
            type: 'object',
            additionalProperties: false,
            properties: {
                title: {
                    type: 'string',
                },
                text: {
                    type: 'string',
                },
            },
            title: 'TotemDisplayName',
        },
        BrandKeywords: {
            type: 'object',
            additionalProperties: false,
            properties: {
                brandNameKeywords: {
                    type: 'array',
                    items: { type: 'string' },
                    default: [],
                },
                brandGroupKeywords: {
                    type: 'array',
                    items: { type: 'string' },
                    default: [],
                },
            },
            required: ['brandNameKeywords', 'brandGroupKeywords'],
            title: 'BrandKeywords',
        },
    },
} as const satisfies JSONSchemaExtraProps;
