{
    "folders": [
        {
            "name": "Root monorepo",
            "path": "../",
        },
        {
            "name": "Back",
            "path": "../apps/app-malou-api",
        },
        {
            "name": "E2E",
            "path": "../apps/app-malou-e2e",
        },
        {
            "name": "Front",
            "path": "../apps/app-malou-web",
        },
        {
            "name": "<PERSON>oupe",
            "path": "../apps/app-maloupe",
        },
        {
            "name": "Store Locator",
            "path": "../apps/store-locator",
        },
        {
            "name": "PModels",
            "path": "../packages/malou-package-models",
        },
        {
            "name": "PDto",
            "path": "../packages/malou-dto",
        },
        {
            "name": "PUtils",
            "path": "../packages/malou-utils",
        },
        {
            "name": "PConfig",
            "path": "../packages/malou-config",
        },
        {
            "name": "PEmails",
            "path": "../packages/malou-emails",
        },
        {
            "name": "PCrawlers",
            "path": "../packages/crawlers",
        },
        {
            "name": "Infrastructure",
            "path": "../infrastructure",
        },
    ],
    "settings": {
        "[terraform]": {
            "editor.defaultFormatter": "hashicorp.terraform",
            "editor.formatOnSave": true,
        },
        "angular.enable-strict-mode-prompt": false,
        "cSpell.userWords": [
            "acompio",
            "adgeolocation",
            "african",
            "agendash",
            "alexa",
            "allmenus",
            "american",
            "americanexpress",
            "americantownscom",
            "Amstramgram",
            "aroundme",
            "asian",
            "auskunft",
            "automations",
            "betterbusinessbureau",
            "bing",
            "birmanie",
            "bistronomic",
            "bistronomique",
            "bowes",
            "bradstreet",
            "branchen",
            "branchenbuch",
            "brownbook",
            "brownbooknet",
            "brunchlunchdinner",
            "bulkwrite",
            "bundes",
            "bundestelefonbuch",
            "burmese",
            "caribbean",
            "casl",
            "centralindex",
            "chamberofcommercecom",
            "chartjs",
            "checkmark",
            "chichewa",
            "citysearch",
            "citysquares",
            "cloudinary",
            "contentsquare",
            "creperie",
            "crowdsourced",
            "cylex",
            "dasoertliche",
            "datasource",
            "deliveroo",
            "desactivated",
            "désenregistrer",
            "dialo",
            "doesnotexist",
            "doesnt",
            "doordash",
            "dragarea",
            "dunandbradstreet",
            "editability",
            "eirphonebookie",
            "elocal",
            "encryptor",
            "eniro",
            "ESOCKETTIMEDOUT",
            "etablissement",
            "exif",
            "experimentations",
            "experimentations",
            "ezlocal",
            "facebook",
            "finderr",
            "firefox",
            "firstname",
            "folderless",
            "formatted",
            "france",
            "freie",
            "friday",
            "fullname",
            "fute",
            "gaelic",
            "gcid",
            "gelbe",
            "gelbeseiten",
            "geohash",
            "geosample",
            "geosamples",
            "giga",
            "gmail",
            "gmaps",
            "goldenpages",
            "goldenpagesie",
            "golocal",
            "googlemybusiness",
            "goudengids",
            "goyellow",
            "growthbook",
            "gueleton",
            "guestcenter",
            "gule",
            "gulesider",
            "headernav",
            "helpch",
            "herold",
            "hitta",
            "hoodspot",
            "horairesdouverture",
            "hotfrog",
            "hotjar",
            "houuuuu",
            "hubspot",
            "icecream",
            "iglobal",
            "imones",
            "imoneslt",
            "impres",
            "incompleted",
            "infobel",
            "infoisinfo",
            "inkbar",
            "insiderpages",
            "insta",
            "instagram",
            "inyourarea",
            "italian",
            "italiano",
            "italianparis",
            "itineris",
            "itinéris",
            "japanese",
            "jimo",
            "justacote",
            "koomio",
            "krak",
            "kurmanji",
            "lafourchette",
            "laposte",
            "lastname",
            "latlng",
            "lattre",
            "lazyload",
            "levenshtein",
            "lietuva",
            "lngs",
            "localcom",
            "localstack",
            "lokaleauskunft",
            "luxembourgish",
            "luxon",
            "maintext",
            "malou",
            "malouapp",
            "malouma",
            "maloupe",
            "mappy",
            "mapquest",
            "mapster",
            "mapstr",
            "marktplatz",
            "marktplatzmittelstand",
            "mediterranean",
            "megaoctet",
            "meine",
            "meinestadt",
            "merchantcircle",
            "metareducer",
            "mittelstand",
            "monday",
            "moneymailer",
            "mylocalservices",
            "mytownie",
            "najisto",
            "najistocz",
            "nanos",
            "nationalhealthservice",
            "navmii",
            "nexistepas",
            "nfcs",
            "ngrx",
            "ngsw",
            "nochoffen",
            "Nom_Etablissement",
            "notif",
            "odia",
            "oeffnungszeiten",
            "oeffnungszeitenbuch",
            "oeffnungszeitencom",
            "oembed",
            "oertliche",
            "officedocument",
            "Öffnungszeiten",
            "olympics",
            "ooreka",
            "openai",
            "opendi",
            "openingstijdennl",
            "openingtimes",
            "opentable",
            "openuren",
            "openxmlformats",
            "Örtliche",
            "ortsdienst",
            "ortsdienstde",
            "otumamiauth",
            "otumamiauth",
            "pagesjaunes",
            "petitfute",
            "pinterest",
            "piolot",
            "pitney",
            "pitneybowes",
            "popin",
            "popins",
            "popularities",
            "privatereviews",
            "propertycapsule",
            "quicktime",
            "qwant",
            "regexes",
            "répondable",
            "reposting",
            "resizer",
            "resizers",
            "resy",
            "safegraph",
            "saturday",
            "seiten",
            "seznam",
            "showmelocal",
            "sidenav",
            "sider",
            "signin",
            "sinhala",
            "sions",
            "skintones",
            "skus",
            "snapchat",
            "socialposts",
            "soleo",
            "solocal",
            "spanish",
            "spreadsheetml",
            "stadt",
            "stadtbranchenbuch",
            "stijden",
            "stranky",
            "subattachments",
            "subcode",
            "sublocality",
            "sunday",
            "tailwindcss",
            "tassigny",
            "telefonbuch",
            "telephonecity",
            "tellows",
            "testcases",
            "testcases",
            "testid",
            "thursday",
            "tiktok",
            "timestampeable",
            "titlecase",
            "tomtom",
            "tonline",
            "toolbelt",
            "topratedlocal",
            "touristics",
            "tripadvisor",
            "tripadvisorreviews",
            "trustpilot",
            "tsyringe",
            "tuesday",
            "tupalo",
            "uber",
            "ubereats",
            "unarchived",
            "unlisten",
            "unreact",
            "unsave",
            "unsubscription",
            "uppy",
            "upsert",
            "upserted",
            "upserting",
            "upserts",
            "upserts",
            "uscitynet",
            "usecase",
            "usecase",
            "usecases",
            "uuidv",
            "uyghur",
            "virtuals",
            "visalietuvalt",
            "waze",
            "wednesday",
            "weshoplocal",
            "wheelsoffortune",
            "wogibtswas",
            "xcss",
            "yalwa",
            "yandex",
            "yellowmap",
            "yellowmoxie",
            "yellowpagecitycom",
            "yellowpagesgoesgreen",
            "yext",
            "ypcom",
            "zelty",
            "zenchef",
            "zipcode",
            "zlate",
            "zlatestrankycz",
            "zlatestrankysk",
        ],
        "editor.codeActionsOnSave": {
            "source.fixAll.eslint": "explicit",
            "source.fixAll.sortJSON": "explicit",
            "source.organizeImports": "explicit",
        },
        "editor.defaultFormatter": "esbenp.prettier-vscode",
        "editor.formatOnSave": true,
        "i18n-ally.enabledFrameworks": ["ngx-translate"],
        "i18n-ally.keepFulfilled": true,
        "i18n-ally.keystyle": "nested",
        "i18n-ally.localesPaths": [
            "apps/app-malou-api/src/modules/mailing/messages",
            "apps/app-malou-api/src/modules/messages",
            "apps/app-malou-api/src/modules/webhooks/platforms/facebook/messages",
            "apps/app-malou-api/src/modules/webhooks/platforms/gmb/messages",
            "apps/app-malou-api/src/tasks/messages",
            "apps/app-malou-web/src/app/modules/messages",
            "apps/app-malou-web/src/assets/i18n",
            "apps/app-maloupe/src/assets/i18n",
            "packages/malou-package-models/lib/modules/messages",
            "packages/malou-package-models/src/modules/messages",
        ],
        "i18n-ally.sortKeys": true,
        "i18n-ally.sourceLanguage": "fr",
        "javascript.preferences.importModuleSpecifier": "non-relative",
        "jestrunner.changeDirectoryToWorkspaceRoot": true,
        "jestrunner.debugOptions": {
            "args": ["--config jest.config.unit.ts", "--forceExit"],
        },
        "jestrunner.jestCommand": "",
        "jestrunner.jestPath": "node_modules/jest/bin/jest.js",
        "jestrunner.runOptions": ["--config jest.config.unit.ts", "--forceExit"],
        "liveServer.settings.multiRootWorkspaceName": "Root monorepo",
        "search.exclude": {
            "**/*.tsbuildinfo": true,
            "**/.DS_Store": true,
            "**/dist": true,
            "**/lib": true,
            "**/node_modules": true,
        },
        "typescript.preferences.importModuleSpecifier": "non-relative",
        "[github-actions-workflow]": {
            "editor.defaultFormatter": "esbenp.prettier-vscode",
        },
        "[astro]": {
            "editor.defaultFormatter": "esbenp.prettier-vscode",
            "editor.formatOnSave": true,
        },
        "[yaml]": {
            "editor.defaultFormatter": "esbenp.prettier-vscode",
        },
        "[typescript]": {
            "editor.defaultFormatter": "esbenp.prettier-vscode",
        },
        "[json]": {
            "editor.tabSize": 4,
            "editor.insertSpaces": false,
        },
        "files.associations": {
            "*.css": "tailwindcss",
        },
    },
}
