import { config } from ':config';
import { capitalize, filterByRequiredKeys } from ':utils/functions';
import { favIconConfig } from ':utils/images/favicons';
import type { APIRoute } from 'astro';

export const GET: APIRoute = async () => {
    const icons = filterByRequiredKeys(Object.values(favIconConfig), [
        'url',
        'sizes',
        'type',
    ]).map(({ sizes, type, url }) => ({
        src: url,
        type,
        sizes,
    }));
    const organizationName = capitalize(config.organizationName);

    const manifest = {
        short_name: organizationName,
        name: organizationName,
        start_url: '/',
        display: 'standalone',
        icons,

        // todo add better values
        description: `${organizationName}'s store locator`,
        theme_color: '#000000',
        background_color: '#ffffff',
    };

    return new Response(JSON.stringify(manifest), {
        headers: {
            'Content-Type': 'application/json',
        },
    });
};
