---
import type { GetStoreLocatorStorePageDto } from '@malou-io/package-dto';

import CallToActions from ':components/CallToActions.astro';
import Descriptions from ':components/Descriptions.astro';
import Gallery from ':components/Gallery.astro';
import Head from ':components/Head.astro';
import Information from ':components/Information.astro';
import Reviews from ':components/Reviews.astro';
import SocialNetworks from ':components/SocialNetworks.astro';
import WhiteMark from ':components/WhiteMark.astro';
import { config } from ':config';
import type { StorePage } from ':interfaces/pages.interfaces';
import { loadComponent } from ':utils/load-component';

// Generate static paths for each restaurant in every company
export async function getStaticPaths() {
    const res = await fetch(
        `${config.apiBaseUrl}/store-locator/${config.organizationId}/stores?api_key=${config.apiKey}`,
    );
    const { data: stores }: { data: GetStoreLocatorStorePageDto[] } =
        await res.json();

    return stores.map((store) => {
        const extendedStore: StorePage = store;

        return {
            params: {
                path: store.relativePath,
            },
            props: { store: extendedStore, stores },
        };
    });
}

// Fetch restaurant details
const { store, stores } = Astro.props;

const [HeaderComponent, FooterComponent] = await Promise.all([
    loadComponent({
        organizationName: store.organizationName,
        component: 'header',
    }),
    loadComponent({
        organizationName: store.organizationName,
        component: 'footer',
    }),
]);
---

<!doctype html>
<html lang={store.lang}>
    <head>
        <Head headBlock={store.headBlock} />
    </head>

    <body class="bg-tertiary pb-24 md:pb-0">
        <HeaderComponent />

        {
            store.informationBlock && (
                <Information
                    information={store.informationBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.galleryBlock && (
                <Gallery
                    galleryBlock={store.galleryBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.reviewsBlock && (
                <Reviews
                    reviewsBlock={store.reviewsBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.socialNetworksBlock && (
                <SocialNetworks
                    socialNetworksBlock={store.socialNetworksBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.callToActionsBlock && (
                <CallToActions
                    callToActionsBlock={store.callToActionsBlock}
                    styles={store.styles}
                />
            )
        }

        {
            store.descriptionsBlock && (
                <Descriptions
                    descriptionsBlock={store.descriptionsBlock}
                    styles={store.styles}
                />
            )
        }

        <FooterComponent stores={stores} />

        {store.shouldDisplayWhiteMark && <WhiteMark styles={store.styles} />}
    </body>
</html>
