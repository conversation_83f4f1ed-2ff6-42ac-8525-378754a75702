import { filterByRequiredKeys } from ':utils/functions';
import axios from 'axios';
import icoEndec from 'ico-endec';
import { promises as fs } from 'node:fs';
import path from 'node:path';
import potrace from 'potrace';
import type { Sharp } from 'sharp';
import sharp from 'sharp';

export const favIconConfig: Record<
    string,
    { url: string; rel: string; type?: string; sizes?: string }
> = {
    ico: {
        url: '/favicon.ico',
        rel: 'icon',
        type: 'image/x-icon',
    },
    svg: {
        url: '/favicon.svg',
        rel: 'icon',
        type: 'image/svg+xml',
    },
    'favicon-16x16': {
        url: '/favicon-16x16.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '16x16',
    },
    'favicon-32x32': {
        url: '/favicon-32x32.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '32x32',
    },
    'android-chrome-36x36': {
        url: '/android-chrome-36x36.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '36x36',
    },
    'android-chrome-48x48': {
        url: '/android-chrome-48x48.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '48x48',
    },
    'android-chrome-72x72': {
        url: '/android-chrome-72x72.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '72x72',
    },
    'android-chrome-96x96': {
        url: '/android-chrome-96x96.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '96x96',
    },
    'android-chrome-144x144': {
        url: '/android-chrome-144x144.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '144x144',
    },
    'android-chrome-192x192': {
        url: '/android-chrome-192x192.png',
        rel: 'icon',
        type: 'image/png',
        sizes: '192x192',
    },
    'apple-touch-icon-57x57': {
        url: '/apple-touch-icon-57x57.png',
        rel: 'apple-touch-icon',
        sizes: '57x57',
    },
    'apple-touch-icon-60x60': {
        url: '/apple-touch-icon-60x60.png',
        rel: 'apple-touch-icon',
        sizes: '60x60',
    },
    'apple-touch-icon-72x72': {
        url: '/apple-touch-icon-72x72.png',
        rel: 'apple-touch-icon',
        sizes: '72x72',
    },
    'apple-touch-icon-76x76': {
        url: '/apple-touch-icon-76x76.png',
        rel: 'apple-touch-icon',
        sizes: '76x76',
    },
    'apple-touch-icon-114x114': {
        url: '/apple-touch-icon-114x114.png',
        rel: 'apple-touch-icon',
        sizes: '114x114',
    },
    'apple-touch-icon-120x120': {
        url: '/apple-touch-icon-120x120.png',
        rel: 'apple-touch-icon',
        sizes: '120x120',
    },
    'apple-touch-icon-144x144': {
        url: '/apple-touch-icon-144x144.png',
        rel: 'apple-touch-icon',
        sizes: '144x144',
    },
    'apple-touch-icon-152x152': {
        url: '/apple-touch-icon-152x152.png',
        rel: 'apple-touch-icon',
        sizes: '152x152',
    },
    'apple-touch-icon-180x180': {
        url: '/apple-touch-icon-180x180.png',
        rel: 'apple-touch-icon',
        sizes: '180x180',
    },
};

export interface FavIcon {
    url: string;
    rel: string;
    sizes?: string;
    type?: string;
}

export async function generateFavIcons(src: string): Promise<void> {
    console.log('Generating favicons...');

    // Create output directory if it doesn't exist
    const outputDir = path.join(process.cwd(), 'public');
    try {
        await fs.access(outputDir);
    } catch (e) {
        console.log(`Favicons folder doesn't exist, creating it...`);
        await fs.mkdir(outputDir, { recursive: true });
    }

    const { data } = await axios.get<Buffer>(src, {
        responseType: 'arraybuffer',
    });
    const image = sharp(data);

    await Promise.all([
        generatePngFavIcons(image),
        generateSvgFavIcon(src),
        generateIcoFavIcon(image),
    ]);
    console.log('Favicons generated successfully.');
}

async function generatePngFavIcons(image: Sharp): Promise<void> {
    await Promise.all(
        filterByRequiredKeys(Object.values(favIconConfig), ['sizes']).map(
            async ({ sizes, url }) => {
                const [width, height] = sizes.split('x').map(Number);

                await image
                    .clone()
                    .resize({
                        fit: 'contain',
                        width,
                        height,
                    })
                    .toFormat('png')
                    .toFile(path.join(process.cwd(), `public${url}`));

                console.log(`Generated ${url} favicon`);
            },
        ),
    );
}

async function generateSvgFavIcon(src: string): Promise<void> {
    const svg: any = await convertToSvg(src);
    await fs.writeFile(path.join(process.cwd(), 'public', 'favicon.svg'), svg);
    console.log(`Generated svg favicon`);
}

async function convertToSvg(src: string) {
    return new Promise((resolve, reject) => {
        potrace.posterize(
            src,
            {
                threshold: 180,
                steps: 4,
            },
            (err: Error | null | undefined, svg: any) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(svg);
                }
            },
        );
    });
}

async function generateIcoFavIcon(image: Sharp): Promise<void> {
    // ICO files contain multiple sizes of the same image

    // Step 1: resize image to common favicon formats
    const { width } = await image.metadata();

    let images: Sharp[] = [];
    if (width) {
        const sizes = [16, 32, 48, 64, 128, 256].filter(
            (size) => size <= width,
        );

        images = sizes.map((size) =>
            image.clone().resize({
                fit: 'contain',
                background: { r: 0, g: 0, b: 0, alpha: 0 },
                width: size,
                height: size,
            }),
        );
    } else {
        images = [image];
    }

    // Step 2: Store and encode them in a ICO image
    const imageBuffers = await Promise.all(
        images.map(async (image) => {
            const { data } = await image
                .toFormat('png')
                .toBuffer({ resolveWithObject: true });
            return data;
        }),
    );
    const icoBuffer = icoEndec.encode(imageBuffers);

    await fs.writeFile(
        path.join(process.cwd(), 'public', 'favicon.ico'),
        icoBuffer,
    );
    console.log(`Generated ico favicon`);
}
