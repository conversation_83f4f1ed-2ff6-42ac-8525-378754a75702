import { config } from ':config';
import { generateFavIcons } from ':utils/images/favicons';
import axios, { type AxiosResponse } from 'axios';
import assert from 'node:assert';
import { promises as fs } from 'node:fs';
import path from 'node:path';

import type { GetStoreLocatorOrganizationConfigurationDto } from '@malou-io/package-dto';

async function generateOrganizationAssets() {
    console.log('Fetching assets from API...');

    const response: AxiosResponse<{
        data: GetStoreLocatorOrganizationConfigurationDto;
    }> = await axios.get(
        `${config.apiBaseUrl}/store-locator/${config.organizationId}/configuration?api_key=${config.apiKey}`,
    );
    const tailwindConfig = response?.data?.data?.tailwindConfig;
    const tailwindClassesMap = response?.data?.data?.tailwindClassesMap;
    const favIconUrl = response?.data?.data?.favIconUrl;

    assert(tailwindConfig);
    assert(tailwindClassesMap);
    assert(favIconUrl);

    await Promise.all([
        generateStyles({ tailwindConfig, tailwindClassesMap }),
        generateFavIcons(favIconUrl),
    ]);
}

async function generateStyles({
    tailwindConfig,
    tailwindClassesMap,
}: {
    tailwindConfig: string;
    tailwindClassesMap: string;
}) {
    console.log('Generating styles configuration...');

    const stylesDir = path.join('src', 'styles');
    await fs.mkdir(stylesDir, { recursive: true });

    await Promise.all([
        fs.writeFile(path.join(stylesDir, 'global.css'), tailwindConfig),
        fs.writeFile(
            path.join(stylesDir, 'classes.mapping.ts'),
            tailwindClassesMap,
        ),
    ]);

    console.log('Styles generated successfully.');
}

// Call the function
generateOrganizationAssets()
    .then(() => {
        console.log('tailwind config files generated successfully.');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error during tailwind config file generation', error);
        process.exit(1);
    });
