export function isNotNil<T>(object: T | null | undefined): object is T {
    return object !== null && object !== undefined;
}

export type NonNullableKeys<T, K extends keyof T> = T & {
    [P in K]-?: NonNullable<T[P]>;
};

export function filterByRequiredKeys<T, K extends keyof T>(
    items: T[],
    requiredKeys: K[],
): NonNullableKeys<T, K>[] {
    return items.filter((item): item is NonNullableKeys<T, K> => {
        return requiredKeys.every(
            (key) => item[key] !== null && item[key] !== undefined,
        );
    });
}

export const capitalize = (str: string) =>
    str.charAt(0).toUpperCase() + str.slice(1) || '';
