---
import <PERSON><PERSON><PERSON><PERSON> from ':assets/logos/malou.svg';
import { initTranslationFunction } from ':i18n/index';
import type { StorePage } from ':interfaces/pages.interfaces';
import { getStyles } from ':utils/get-element-styles';

// To type Astro component props, we have to name the interface "Props" literally
interface Props {
    styles: StorePage['styles'];
}

const { styles } = Astro.props as Props;

const getElementStyles = getStyles({ styles });

const t = await initTranslationFunction();
---

<div
    class={`${getElementStyles({ elementId: 'white-mark-wrapper' })} p-8 text-md`}
>
    <a href="https://malou.io/" class="flex items-center justify-center">
        <span>{t('white-mark.powered-by')}</span>
        <MalouLogo
            class={`${getElementStyles({ elementId: 'white-mark-logo' })} h-[15px] w-auto ml-1`}
        />
    </a>
</div>
