import { z } from 'astro/zod';
import dotenv from 'dotenv';
import { loadEnv } from 'vite';

const envValidator = z.object({
    ORGANIZATION_ID: z.string(),
    ORGANIZATION_BASE_URL: z.string(),
    ORGANIZATION_NAME: z.string(),
    API_KEY: z.string(),
    API_BASE_URL: z.string(),
    ENVIRONMENT: z.enum(['production', 'test']).default('test'),
});

const validateEnv = (env: Record<string, unknown>) => {
    const parsedEnv = envValidator.safeParse(env);

    if (!parsedEnv.success) {
        console.error(
            'Invalid environment variables:',
            parsedEnv.error.format(),
        );
        throw new Error('Invalid environment variables');
    }

    return parsedEnv.data;
};

let envVariables: z.infer<typeof envValidator>;
if (typeof import.meta !== 'undefined' && import.meta.env) {
    // Vite/Astro context
    envVariables = validateEnv(
        loadEnv(process.env.NODE_ENV || 'local', process.cwd(), ''),
    );
} else {
    // Node.js context - use dotenv
    dotenv.config({
        path: `.env.${process.env.NODE_ENV || 'local'}`,
    });
    envVariables = validateEnv(process.env);
}

const config = {
    organizationId: envVariables.ORGANIZATION_ID,
    organizationName: envVariables.ORGANIZATION_NAME,
    organizationBaseUrl: envVariables.ORGANIZATION_BASE_URL,
    apiBaseUrl: envVariables.API_BASE_URL,
    apiKey: envVariables.API_KEY,
    environment: envVariables.ENVIRONMENT,
};

export { config };
