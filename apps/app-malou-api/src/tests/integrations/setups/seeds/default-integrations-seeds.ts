import { container } from 'tsyringe';

import { CaslRole } from '@malou-io/package-utils';

import { Config } from ':config';
import { BricksGeneratorRepository } from ':modules/brick-generators/brick-generators.repository';
import { BricksRepository } from ':modules/bricks/bricks.repository';
import CalendarEventsRepository from ':modules/calendar-events/calendar-events.repository';
import CategoriesRepository from ':modules/categories/categories.repository';
import CredentialsRepository from ':modules/credentials/credentials.repository';
import FeedbacksRepository from ':modules/feedbacks/feedback.repository';
import { MediasRepository } from ':modules/media/medias.repository';
import MentionsRepository from ':modules/mentions/mentions.repository';
import NfcsRepository from ':modules/nfc/nfcs.repository';
import OrganizationsRepository from ':modules/organizations/organizations.repository';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import PostsRepository from ':modules/posts/posts.repository';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { UsersRepository } from ':modules/users/users.repository';

import { mockPassport } from '../middlewares/mock-passport';
import {
    brickGenerators,
    bricks,
    calendarEvents,
    categories,
    credentials,
    feedbacks,
    media,
    mentions,
    nfcs,
    organizations,
    platforms,
    posts,
    restaurants,
    users,
} from '../mongo-seed/tests';

const {
    tests: { mockRestaurantId, mockUserId },
} = Config;

export async function seedWithDefaultIntegrationsSetup({ mockPassportWithDefaultUser = false }) {
    const brickRepo = container.resolve(BricksRepository);
    const brickRepoGen = container.resolve(BricksGeneratorRepository);
    const calendarRepo = container.resolve(CalendarEventsRepository);
    const categoryRepo = container.resolve(CategoriesRepository);
    const credRepo = container.resolve(CredentialsRepository);
    const mediaRepo = container.resolve(MediasRepository);
    const orgaRepo = container.resolve(OrganizationsRepository);
    const platformRepo = container.resolve(PlatformsRepository);
    const restauRepo = container.resolve(RestaurantsRepository);
    const userRepo = container.resolve(UsersRepository);
    const mentionRepo = container.resolve(MentionsRepository);
    const feedbackRepo = container.resolve(FeedbacksRepository);
    const postRepo = container.resolve(PostsRepository);
    const nfcRepo = container.resolve(NfcsRepository);

    await Promise.all([
        brickRepo.createMany(bricks as any),
        brickRepoGen.createMany(brickGenerators as any),
        calendarRepo.createMany(calendarEvents as any),
        categoryRepo.createMany(categories as any),
        credRepo.createMany(credentials as any),
        mediaRepo.createMany(media as any),
        orgaRepo.createMany(organizations as any),
        platformRepo.createMany(platforms as any),
        restauRepo.createMany(restaurants as any),
        userRepo.createMany(users as any),
        mentionRepo.createMany(mentions as any),
        feedbackRepo.createMany(feedbacks as any),
        postRepo.createMany(posts as any),
        nfcRepo.createMany(nfcs as any),
    ]);

    if (mockPassportWithDefaultUser) {
        mockPassport({
            _id: mockUserId,
            restaurants: [{ restaurantId: mockRestaurantId, restaurant: { organizationId: '61e533f8f257b72f0bc3d52b' } }],
            organizations: [{ _id: '61e533f8f257b72f0bc3d52b' }],
            caslRole: CaslRole.OWNER,
        });
    }
}
