/* eslint-disable no-empty-function */
import { AbilityBuilder } from '@casl/ability';

import { AppAbility, AppCaslRule, CaslRole, MalouErrorCode, Role, userAbilities, userRestaurantAbilities } from '@malou-io/package-utils';

import { MalouError } from ':helpers/classes/malou-error';

export const defineRulesForUser = async (user: {
    role: Role;
    caslRole: CaslRole;
    organizations: { _id: string }[];
    organizationIds: string[];
}): Promise<AppCaslRule[]> => {
    const { can, rules } = new AbilityBuilder(AppAbility);

    const caslRole = _getUserCaslRole(user);
    if (typeof userAbilities[caslRole] === 'function') {
        userAbilities[caslRole](user, can);
    } else {
        throw new MalouError(MalouErrorCode.CASL_UNKNOWN_ROLE, {
            message: 'Trying to use unknown role',
            metadata: { caslRole: user.caslRole, role: 'user' },
        });
    }
    return rules;
};

export const defineRulesForUserRestaurant = async (userRestaurant: {
    user: { role: Role };
    caslRole: CaslRole;
    restaurantId: string;
    restaurant: { organizationId?: string };
}): Promise<AppCaslRule[]> => {
    const { can, rules } = new AbilityBuilder(AppAbility);

    const caslRole = _getUserRestaurantCaslRole(userRestaurant);
    if (typeof userRestaurantAbilities[caslRole] === 'function') {
        userRestaurantAbilities[caslRole](userRestaurant, can);
    } else {
        throw new MalouError(MalouErrorCode.CASL_UNKNOWN_ROLE, {
            message: 'Trying to use unknown role',
            metadata: { caslRole: userRestaurant.caslRole, role: 'userRestaurant' },
        });
    }
    return rules;
};

const _getUserCaslRole = (user: { role: Role; caslRole: CaslRole }): CaslRole => {
    if (!user) throw new MalouError(MalouErrorCode.CASL_USER_NOT_DEFINED, { message: 'User is not defined' });
    if (user.role === Role.ADMIN) return CaslRole.ADMIN; // bypass everything if user has admin role
    return user.caslRole || CaslRole.GUEST;
};

const _getUserRestaurantCaslRole = (userRestaurant: { user: { role: Role }; caslRole: CaslRole }): CaslRole => {
    if (!userRestaurant?.user)
        throw new MalouError(MalouErrorCode.CASL_USER_NOT_DEFINED, { message: 'Need to populate user on UserRestaurant' });
    if (userRestaurant.user.role === Role.ADMIN) return CaslRole.ADMIN; // bypass everything if user has admin role
    return userRestaurant.caslRole || CaslRole.GUEST;
};
