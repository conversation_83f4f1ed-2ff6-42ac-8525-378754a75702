import 'reflect-metadata';

import ':env';

import lodash from 'lodash';
import { container, singleton } from 'tsyringe';

import { waitFor } from '@malou-io/package-utils';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { ComputeRestaurantCompletionScoreService } from ':modules/restaurants/services/compute-completion-score.service';

@singleton()
class GetRestaurantsCompletionScore {
    private readonly _CHUNK_SIZE = 20;
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _computeRestaurantCompletionScoreService: ComputeRestaurantCompletionScoreService
    ) {}

    async execute(): Promise<void> {
        const allRestaurants = await this._restaurantsRepository.find({
            filter: {},
            projection: { _id: 1, name: 1 },
            options: { lean: true },
        });
        logger.info(`${allRestaurants.length} restaurants to be processed`);

        let restaurantsCount = 0;
        const scores: number[] = [];
        const scoreMapping: Record<string, number> = {};

        for (const restaurants of lodash.chunk(allRestaurants, this._CHUNK_SIZE)) {
            const newScores = await Promise.all(
                restaurants.map(async ({ _id, name }) => {
                    try {
                        const score = await this._computeRestaurantCompletionScoreService.execute({
                            restaurantId: _id.toString(),
                            shouldUpdate: true,
                        });

                        scoreMapping[_id.toString()] = score;
                        logger.info(`Restaurant ${name} has a completion score of ${score}`);

                        return score;
                    } catch (error: Error | any) {
                        logger.error(error, { metadata: error?.metadata, restaurantId: _id.toString(), name });
                        return -1;
                    }
                })
            );

            scores.push(...newScores);
            restaurantsCount += restaurants.length;
            logger.info(`${restaurantsCount} restaurants processed`);

            // We have to await that long to avoid being rate limited by Google (when checking allowed attributes per category)
            await waitFor(5000);
        }

        logger.info(
            `${restaurantsCount} restaurants processed, ${scores.length} scores calculated, ${
                Object.keys(scoreMapping).length
            } scores in mapping`
        );
        if (scores.find((score) => score > 100)) {
            logger.warn('Some scores are above 100');
        }

        const stats = this._getStats(scores);
        logger.info('Completion score stats', stats);
    }

    private _getStats(scores: number[]) {
        const minScore = Math.min(...scores);
        const maxScore = Math.max(...scores);
        const range = maxScore - minScore;
        const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
        const median = this._computeMedian(scores);
        const { variance, standardDeviation } = this._computeVarianceAndStandardDeviation(scores, mean);

        return {
            minScore,
            maxScore,
            mean,
            median,
            range,
            variance,
            standardDeviation,
        };
    }

    private _computeMedian(scores: number[]): number {
        const sorted = [...scores].sort((a, b) => a - b);
        const mid = Math.floor(sorted.length / 2);

        return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
    }

    private _computeVarianceAndStandardDeviation(scores: number[], mean: number): { variance: number; standardDeviation: number } {
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
        const standardDeviation = Math.sqrt(variance);

        return { variance, standardDeviation };
    }
}

const task = container.resolve(GetRestaurantsCompletionScore);

task.execute()
    .then(() => {
        console.log('Task completed');
        process.exit(0);
    })
    .catch((error) => {
        console.error('Error while executing task', error);
        process.exit(1);
    });
