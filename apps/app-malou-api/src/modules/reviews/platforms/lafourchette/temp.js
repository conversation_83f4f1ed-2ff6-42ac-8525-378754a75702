import axios from 'axios';

const url = 'https://www.doordash.com/graphql/getConsumerReviewDetails?operation=getConsumerReviewDetails';

// ispProxyUsername: 'brd-customer-hl_94fbfe64-zone-isp_proxy1',
//     ispProxyPassword: process.env.BRIGHT_DATA_ISP_PROXY_PASSWORD,
//         residentialProxyUsername: 'brd-customer-hl_94fbfe64-zone-residential_proxy1',
//             residentialProxyPassword: process.env.BRIGHT_DATA_RESIDENTIAL_PROXY_PASSWORD,
//                 proxyHost: 'brd.superproxy.io',
//                     proxyPort: 22225,
//BRIGHT_DATA_ISP_PROXY_PASSWORD=ot175jn3aelt
//BRIGHT_DATA_RESIDENTIAL_PROXY_PASSWORD=s95luy9g5bnd

const proxy = {
    protocol: 'http',
    host: 'brd.superproxy.io',
    port: 22225,
    auth: {
        // username: 'brd-customer-hl_94fbfe64-zone-isp_proxy1',
        // password: 'ot175jn3aelt',
        username: 'brd-customer-hl_94fbfe64-zone-residential_proxy1',
        password: 's95luy9g5bnd',
    },
};

const headers = {
    "accept": "*/*",
    "accept-language": "en-US",
    "apollographql-client-name": "@doordash/app-consumer-production-ssr-client",
    "apollographql-client-version": "3.0",
    "baggage": "sentry-environment=production,sentry-release=consumer-web-next%402.8328.0,sentry-public_key=f55609756bfb481c8ad0a180c8248883,sentry-trace_id=6207b0e92035468ca61e6c8febe4268e,sentry-sample_rate=0.2,sentry-sampled=false",
    "cache-control": "no-cache",
    "content-type": "application/json",
    "pragma": "no-cache",
    "priority": "u=1, i",
    "sec-ch-ua": "\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
    "sec-ch-ua-arch": "\"arm\"",
    "sec-ch-ua-bitness": "\"64\"",
    "sec-ch-ua-full-version": "\"137.0.7151.120\"",
    "sec-ch-ua-full-version-list": "\"Google Chrome\";v=\"137.0.7151.120\", \"Chromium\";v=\"137.0.7151.120\", \"Not/A)Brand\";v=\"24.0.0.0\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-model": "\"\"",
    "sec-ch-ua-platform": "\"macOS\"",
    "sec-ch-ua-platform-version": "\"15.5.0\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "sentry-trace": "6207b0e92035468ca61e6c8febe4268e-97ab1e5caaebb148-0",
    "traceparent": "00-d49896d74a502c3365c9994eb3edb6b1-8551c3f40c3cf3d1-01",
    'user-agent': 'curl/7.79.1',
    // 'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    "x-channel-id": "marketplace",
    "x-csrftoken": "",
    "x-experience-id": "doordash",
    // "cookie": "portal-web.sid=s%3A3R10M1JE5I1P-cSjNcE0UbFmImTDaEl1.kTWMgKEA2lvxzUJzAwJu5Jxl0LZOgej7tRMeVNP8aR4;",
    "cookie": "__cf_bm=aCS8KBeh1y4AwIx6EW0aE2a0eGhXtSTpvhwoGlcs278-1750840753-*******-BLA1Cesz_EtNp.CbotVxz3sGnxaFhXA5fh0OfgqWgYT8WvmHEEdq.fNAIB9NjdjSAHxK1.R61RG.F88ItLzUbCYrAf.kf8eL4rB3riPbi3KOXQyIQets5CdOCxdDv6wH; _cfuvid=A5b6gJhQi6sxVUv9QBggs6I63290HflhS8U7PjFeWC8-1750840754238-*******-604800000; ddweb_session_id=e091ca79-de11-44a4-a62a-d40a0cc1448a:1:0197a63d-c64d-7085-901f-34e5048da8f3; dd_delivery_correlation_id=938416a1-3856-4735-900e-11e6735c3e92; dd_device_id=dx_6411e03834c3439c9812e427e5cfa5ba; dd_device_session_id=90e2b712-fe6b-4adb-81cb-819d7a6bb76a; dd_session_id=sx_b92a0d76754b46cdb29107d378b12ec8; authState=ff4c8c89-c9f8-4d2d-b3ec-766801fd557b; dd_market_id=-1; cf_clearance=td.QvXWN1QPhnLZ4X83kB52Dg6.IuyElBDUzXyZP1_I-1750840756-*******-E0EWRUgyLNGeFyik6Oyv0.tlL7x24fEJtYSVXQcNPPPlWa3auu._lene5OdPLmG6UTPRagKYGv3BvNX_N7b5KxI5Xyptf5q6oWtYoj5QkWsb7.QMO0ArFvzaISMw4bxzeLKujBfcdWoT6ELCfoYvcduRWj8dlAQuw3m0BBQw9F1Jw59o3OQ2KP2s3yhoaAvLC1i7aJZSfTDzZdoaqFCACXZ8Ng.SI5BnkXDL6mGDi5NLdcOmE00S0w0NcrHTONnUwLXrwH1lKWVvV9hm018lDBoMaVKQJPSLWdyCrSLKdnvUzoNfzuu5oFBhBhMNTqvC9sA8VOF06hMYNfjUoQpU9g8ssyX9gKfEKE6FvSr9mkXvb1M59tViTiGLvlxEJokU; dd_market_id=-1; realtime_events=W3siYWN0aW9uX3R5cGUiOiJzdG9yZV92aXNpdCIsImVudGl0eV9pZCI6Ijg1MDY1NiJ9XQ%3D%3D; rskxRunCookie=0; rCookie=pqgo4pfzx6lxyf28ltr7oemcbpdqnf; lastRskxRun=1750840757560; __ssid=565652e4fb479ae335c42d2c3c56d60; __cf_bm=TjWax5w7YNvojXWJ4QXmGURNQg9eQc3nqumDmeYUeQ8-1750841527-*******-POr68eg8k1sON_tLVJTwZZY_KcJpXqfnEj8_.YyHkCyKuklmLLUL8chAl0kC1Jrlqn1rHX2ME04f8gHI0tdUSTNasqWtPh78fhx2bdEAd2U; _cfuvid=uksOUx6ULKDyifXQziS7fude_eKrD6Tbzmy1WYfJT9k-1750841527759-*******-604800000",
    "Referer": "https://www.doordash.com/store/wingstop-brooklyn-850656/35521353/",
    "origin": "https://www.doordash.com",
    "Referrer-Policy": "strict-origin-when-cross-origin"
};

const data = {
    operationName: 'getConsumerReviewDetails',
    variables: {
        consumerReviewUuidsList: ['bb1bd333-ad86-4a46-b4f7-945eedd63d72'],
        consumerReviewSource: 'CONSUMER_REVIEW_SOURCE_DOORDASH',
    },
    query: `
    query getConsumerReviewDetails($consumerReviewUuidsList: [ID!]!, $consumerReviewSource: ConsumerReviewSourceEnum) {
      getConsumerReviewDetails(
        consumerReviewUuidsList: $consumerReviewUuidsList
        consumerReviewSource: $consumerReviewSource
      ) {
        result {
          reviewsList {
            consumerReviewUuid
            reviewerDisplayName
            numStars
            reviewText
            reviewedAt
            isVerified
            experience
            helpfulCount
            markedHelpful
            storeId
            markedUpReviewText
            taggedItems { id name }
            itemsList {
              id
              name
              image { url }
              price {
                unitAmount currency displayString decimalPlaces sign symbol
              }
              ratingInfo { ratingType ratingValue }
              ratingDisplayString
            }
            consumerReviewSource
            reviewerData {
              id creatorProfileUri displayName description
              profileImage { url }
              isVerified categoryHighlightTag
            }
            fullReviewUrl
            reviewPhotoDetails {
              title subtitle photoUuid photoUrl createdAt
              reviewerData { displayName profileImage { url } }
              photoTaggedItems {
                itemId itemIdStr itemName
                price { currency displayString decimalPlaces unitAmount sign symbol }
                image { url }
                categoryName
              }
            }
          }
        }
      }
    }
  `,
};

const requestConfig = {
    method: 'POST',
    url,
    headers,
    data,
    proxy,
};

axios.request(requestConfig).then(response => {
    console.log(JSON.stringify(response.data, null, 2));
})
    .catch(error => {
        console.error('Request failed:', error.response?.status, error.response?.data);
    });