import { AddressDto } from '@malou-io/package-dto';
import { IRestaurant } from '@malou-io/package-models';
import { CountryCode } from '@malou-io/package-utils';

type IAddress = NonNullable<IRestaurant['address']>;

export class Address implements IAddress {
    streetNumber?: string;
    route?: string;
    formattedAddress?: string;
    administrativeArea?: string;
    locality: string;
    regionCode: CountryCode;
    country: string;
    postalCode?: string;

    constructor(address: IAddress) {
        this.streetNumber = address.streetNumber;
        this.route = address.route;
        this.formattedAddress = address.formattedAddress;
        this.administrativeArea = address.administrativeArea;
        this.locality = address.locality ?? '';
        this.regionCode = address.regionCode;
        this.country = address.country;
        this.postalCode = address.postalCode;
    }

    toDto(): AddressDto {
        return {
            streetNumber: this.streetNumber,
            route: this.route,
            formattedAddress: this.formattedAddress,
            administrativeArea: this.administrativeArea,
            locality: this.locality,
            regionCode: this.regionCode,
            country: this.country,
            postalCode: this.postalCode,
        };
    }
}
