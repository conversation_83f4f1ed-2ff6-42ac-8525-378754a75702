import 'reflect-metadata';

import ':env';

import { singleton } from 'tsyringe';

import { IAttribute, IRestaurant, IRestaurantAttributeWithAttribute, OverwriteOrAssign, toDbId } from '@malou-io/package-models';
import { BusinessCategory, DescriptionSize, isNotNil } from '@malou-io/package-utils';

import AttributesUseCases from ':modules/attributes/attributes.use-cases';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';

type IRestaurantWithAttributes = OverwriteOrAssign<IRestaurant, { attributeList: IRestaurantAttributeWithAttribute[] }>;

@singleton()
export class ComputeRestaurantCompletionScoreService {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _attributesUseCases: AttributesUseCases
    ) {}

    // This score is computed according to the same rules as in informations-gauge.component.ts
    // If you change the rules here, please make sure to update the ones in this component and in the task responsible for the completion score computation: GetRestaurantsCompletionScore
    async execute({ restaurantId, shouldUpdate }: { restaurantId: string; shouldUpdate?: boolean }): Promise<number> {
        const [restaurant, attributes] = await Promise.all([
            this._restaurantsRepository.findOneOrFail({
                filter: { _id: toDbId(restaurantId) },
                options: {
                    lean: true,
                    populate: [{ path: 'attributeList', populate: [{ path: 'attribute' }] }],
                },
            }),
            this._getAttributesForCategory(restaurantId),
        ]);

        const score = this._getCompletionScore(restaurant, attributes);

        if (shouldUpdate) {
            await this._restaurantsRepository.updateOne({
                filter: { _id: restaurant._id },
                update: { completionScore: score },
            });
        }

        return score;
    }

    private async _getAttributesForCategory(restaurantId: string): Promise<IAttribute[]> {
        try {
            return await this._attributesUseCases.getAttributesForCategory(restaurantId);
        } catch (err) {
            return [];
        }
    }

    private _getCompletionScore(restaurant: IRestaurantWithAttributes, attributes: IAttribute[]): number {
        let score = 0;
        const isLocalRestaurant = restaurant.type === BusinessCategory.LOCAL_BUSINESS;

        // First criteria
        if (restaurant.name) {
            score += isLocalRestaurant ? 10 : 20;
        }

        // Second criteria
        if (isLocalRestaurant) {
            const hasAddress =
                !!restaurant.address?.locality &&
                !!restaurant.address?.country &&
                !!restaurant.address?.postalCode &&
                !!restaurant.address?.regionCode;

            if (hasAddress) {
                score += 10;
            }
        }

        // Third criteria
        if (isNotNil(restaurant.category)) {
            score += isLocalRestaurant ? 10 : 20;
        }

        // Fourth criteria
        if (isLocalRestaurant) {
            if ((restaurant.regularHours?.length ?? 0) > 0) {
                score += 10;
            }
        }

        // Fifth criteria
        if (isLocalRestaurant) {
            const hasPhone = !!restaurant?.phone?.digits && !!restaurant?.phone?.prefix;
            if (hasPhone) {
                score += 10;
            }
        }

        // Sixth criteria
        const SHORT_DESCRIPTION_SIZE_MIN = 60;
        const hasShortDescription = restaurant.descriptions.some(
            (desc) => desc.size === DescriptionSize.SHORT && !!desc.text && desc.text?.length > SHORT_DESCRIPTION_SIZE_MIN
        );
        const LONG_DESCRIPTION_SIZE_MIN = 400;
        const hasLongDescription = restaurant.descriptions.some(
            (desc) => desc.size === DescriptionSize.LONG && !!desc.text && desc.text?.length > LONG_DESCRIPTION_SIZE_MIN
        );

        if (hasShortDescription && (!isLocalRestaurant || hasLongDescription)) {
            score += isLocalRestaurant ? 10 : 20;
        }

        // Seventh criteria
        const SECONDARY_CATEGORIES_MIN = 2;
        if (restaurant.categoryList?.length >= SECONDARY_CATEGORIES_MIN) {
            score += 10;
        }

        // Eighth criteria
        if (isLocalRestaurant) {
            const allPossibleAttributesLength = attributes?.length;
            const restaurantAttributesLength = restaurant.attributeList?.length;

            const hasEnoughAttributesSet =
                allPossibleAttributesLength > 0 &&
                restaurantAttributesLength > 0 &&
                restaurantAttributesLength / allPossibleAttributesLength >= 0.8;

            if (hasEnoughAttributesSet) {
                score += 10;
            }
        }

        // Ninth criteria
        const hasAtLeastOneLink =
            !!restaurant.website ||
            !!restaurant.reservationUrl ||
            !!restaurant.menuUrl ||
            !!restaurant.orderUrl ||
            !!restaurant.socialNetworkUrls?.some((socialNetworkUrl) => !!socialNetworkUrl.url);
        if (hasAtLeastOneLink) {
            score += isLocalRestaurant ? 10 : 20;
        }

        // Tenth criteria
        if (isNotNil(restaurant.logo)) {
            score += 10;
        }

        return score;
    }
}
