import { Router } from 'express';
import { singleton } from 'tsyringe';

import {
    DuplicatePostV2BodyDto,
    DuplicatePostV2ParamsDto,
    SocialPostDto,
    SwapPlannedPublicationDatesBodyDto,
    TransformToReelParamsDto,
    UpdatePlannedPublicationDateBodyDto,
    UpdatePlannedPublicationDateParamsDto,
} from '@malou-io/package-dto';

import { casl } from ':helpers/casl/middlewares';
import { RequestWithPermissions, RequestWithUser } from ':helpers/utils.types';
import PostsController from ':modules/posts/v2/posts.controller';
import { authorize } from ':plugins/passport';

@singleton()
export default class PostsRouterV2 {
    private readonly _prefix = '/posts/v2';

    constructor(private readonly _postsController: PostsController) {}

    init(router: Router): void {
        router.get(`${this._prefix}/social-posts`, authorize(), (req, res, next) =>
            this._postsController.handleGetSocialPostsByIds(req, res, next)
        );

        router.get(`${this._prefix}/to-duplicate`, authorize(), (req, res, next) =>
            this._postsController.handleGetPostsToDuplicate(req, res, next)
        );

        router.get(`${this._prefix}/:postId`, authorize(), (req, res, next) => this._postsController.handleGetPostById(req, res, next));

        router.get(`${this._prefix}/restaurants/:restaurant_id/social-posts`, authorize(), (req, res, next) =>
            this._postsController.handleGetSocialPosts(req, res, next)
        );

        router.get(`${this._prefix}/restaurants/:restaurant_id/social-posts-counts`, authorize(), (req, res, next) =>
            this._postsController.handleGetSocialPostsCounts(req, res, next)
        );

        router.get(`${this._prefix}/restaurants/:restaurantId/user-tags-history`, authorize(), (req, res, next) =>
            this._postsController.handleGetRestaurantUserTagsHistory(req, res, next)
        );

        router.get(`${this._prefix}/restaurants/:restaurantId/instagram-collaborators-history`, authorize(), (req, res, next) =>
            this._postsController.handleGetRestaurantInstagramCollaboratorsHistory(req, res, next)
        );

        router.get(`${this._prefix}/restaurants/:restaurant_id/feed`, authorize(), (req, res, next) =>
            this._postsController.handleGetFeed(req, res, next)
        );

        router.get(
            `${this._prefix}/restaurants/:restaurant_id/programmed-social-post-platform-keys-by-date`,
            authorize(),
            (req, res, next) => this._postsController.handleGetProgrammedSocialPostPlatformKeysByDate(req, res, next)
        );

        router.post(
            `${this._prefix}/restaurants/:restaurant_id/duplicate`,
            authorize(),
            casl(),
            (req: RequestWithPermissions<DuplicatePostV2ParamsDto, never, DuplicatePostV2BodyDto>, res, next) =>
                this._postsController.handleDuplicatePost(req, res, next)
        );

        router.post(`${this._prefix}/poll-status`, authorize(), (req, res, next) =>
            this._postsController.handlePollingPostsStatuses(req, res, next)
        );

        router.post(`${this._prefix}`, authorize(), (req, res, next) => this._postsController.handleCreateSocialPost(req, res, next));

        router.put(
            `${this._prefix}/:post_id/planned-publication-date`,
            authorize(),
            casl(),
            (
                req: RequestWithPermissions<UpdatePlannedPublicationDateParamsDto, never, never, UpdatePlannedPublicationDateBodyDto>,
                res,
                next
            ) => this._postsController.handleUpdateSocialPostPlannedPublicationDate(req, res, next)
        );

        router.put(
            `${this._prefix}/:post_id/transform-to-reel`,
            authorize(),
            casl(),
            (req: RequestWithPermissions<TransformToReelParamsDto>, res, next) =>
                this._postsController.handleTransformPostToReel(req, res, next)
        );

        router.put(
            `${this._prefix}/swap-planned-publication-dates`,
            authorize(),
            casl(),
            (req: RequestWithPermissions<never, never, SwapPlannedPublicationDatesBodyDto>, res, next) =>
                this._postsController.handleSwapPlannedPublicationDates(req, res, next)
        );

        router.put(`${this._prefix}`, authorize(), casl(), (req: RequestWithUser<any, any, SocialPostDto>, res, next) =>
            this._postsController.handleUpdateSocialPost(req, res, next)
        );

        router.post(`${this._prefix}/delete`, authorize(), (req, res, next) =>
            this._postsController.handleDeleteSocialPosts(req, res, next)
        );

        router.get(`${this._prefix}/:postId/refresh`, authorize(), (req, res, next) =>
            this._postsController.handleRefreshSocialPost(req, res, next)
        );

        router.post(`${this._prefix}/:postId/publish-now`, authorize(), (req, res, next) =>
            this._postsController.handlePublishPostNow(req, res, next)
        );
    }
}
