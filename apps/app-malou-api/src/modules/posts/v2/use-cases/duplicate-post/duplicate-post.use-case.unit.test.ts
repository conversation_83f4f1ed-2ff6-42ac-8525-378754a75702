import { Ability, AbilityBuilder } from '@casl/ability';
import { DateTime } from 'luxon';
import { container } from 'tsyringe';

import { DuplicatePostV2BodyDto, DuplicatePostV2ResponseDto } from '@malou-io/package-dto';
import { newDbId, toDbId } from '@malou-io/package-models';
import {
    CallToActionType,
    CaslAction,
    CaslSubject,
    getSocialPlatformKeysWithPost,
    HashtagType,
    MalouErrorCode,
    MapstrCtaButtonType,
    PlatformKey,
    PostPublicationStatus,
    PostSource,
    SeoPostTopic,
    TiktokPrivacyStatus,
} from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { registerOtherDependencies, registerRepositories, TestCaseBuilderV2 } from ':helpers/tests/testing-utils';
import { TiktokQueryCreatorInfoUseCase } from ':modules/posts/platforms/tiktok/use-cases/tiktok-query-creator-info.use-case';
import { getDefaultPost } from ':modules/posts/tests/posts.builder';
import { DuplicatePostUseCase } from ':modules/posts/v2/use-cases/duplicate-post/duplicate-post.use-case';
import { getDefaultRestaurant } from ':modules/restaurants/tests/restaurant.builder';

jest.mock(':plugins/redis-db');

describe('DuplicatePostUseCase', () => {
    beforeEach(() => {
        container.reset();
        registerRepositories(['PostsRepository', 'RestaurantsRepository']);
        registerOtherDependencies();
        const TiktokQueryCreatorInfoUseCaseMock = {
            execute: jest.fn().mockResolvedValue({
                creatorUserName: 'tiktok-username',
                privacyLevelOptions: Object.values(TiktokPrivacyStatus),
                commentDisabled: false,
                duetDisabled: false,
                stitchDisabled: false,
            }),
        } as unknown as TiktokQueryCreatorInfoUseCase;
        container.registerInstance(TiktokQueryCreatorInfoUseCase, TiktokQueryCreatorInfoUseCaseMock);
    });

    describe('Error cases', () => {
        it('should throw an error if a post does not exist', async () => {
            const useCase = container.resolve(DuplicatePostUseCase);

            await expect(
                useCase.execute({
                    restaurantIds: [],
                    postIdsToDuplicate: [newDbId().toString()],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId: newDbId().toString(),
                    userRestaurantsAbility: new AbilityBuilder(Ability).build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ malouErrorCode: MalouErrorCode.POST_NOT_FOUND }));
        });

        it('should throw an error if the user does not have the permission to publish the post', async () => {
            const useCase = container.resolve(DuplicatePostUseCase);

            await expect(
                useCase.execute({
                    restaurantIds: [newDbId().toString()],
                    postIdsToDuplicate: [],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId: newDbId().toString(),
                    userRestaurantsAbility: new AbilityBuilder(Ability).build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ name: 'ForbiddenError' }));
        });

        it('should throw an error if the post does not belong to the restaurant', async () => {
            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('1').build(), getDefaultRestaurant().uniqueKey('2').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [getDefaultPost().restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const postId = seededObjects.posts[0]._id.toString();
            const fromRestaurantId = seededObjects.restaurants[1]._id.toString();
            const toRestaurantId = seededObjects.restaurants[0]._id.toString();

            const useCase = container.resolve(DuplicatePostUseCase);

            const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
            userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });

            await expect(
                useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ malouErrorCode: MalouErrorCode.BAD_REQUEST }));
        });

        it('should throw an error if the user does not have permission to create social post', async () => {
            const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                seeds: {
                    restaurants: {
                        data() {
                            return [getDefaultRestaurant().uniqueKey('1').build()];
                        },
                    },
                    posts: {
                        data(dependencies) {
                            return [getDefaultPost().keys([PlatformKey.INSTAGRAM]).restaurantId(dependencies.restaurants()[0]._id).build()];
                        },
                    },
                },
            });

            await testCase.build();

            const seededObjects = testCase.getSeededObjects();
            const post = seededObjects.posts[0];
            const postId = post._id.toString();
            const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
            const toRestaurantId = seededObjects.restaurants[0]._id.toString();

            const useCase = container.resolve(DuplicatePostUseCase);

            const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
            userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });

            await expect(
                useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author: { id: newDbId().toString(), name: 'John Doe' },
                })
            ).rejects.toThrow(expect.objectContaining({ name: 'ForbiddenError' }));
        });
    });

    describe('Success cases', () => {
        describe('Duplicate social posts in same restaurant as social posts', () => {
            it('should duplicate a simple draft post in the same restaurant as social post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .source(PostSource.SOCIAL)
                                        .text('Duplicate me please!')
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];

                        return {
                            socialPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: post.title,
                                        text: post.text ?? '',
                                        platformKeys: post.keys ?? [],
                                        published: post.published,
                                        isPublishing: false,
                                        postType: post.postType,
                                        location: post.location ?? null,
                                        callToAction: post.callToAction
                                            ? {
                                                  actionType: post.callToAction.actionType as MapstrCtaButtonType,
                                                  url: post.callToAction.url ?? '',
                                              }
                                            : null,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: post.hashtags
                                            ? {
                                                  selected:
                                                      post.hashtags.selected?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                                  suggested:
                                                      post.hashtags.suggested?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                              }
                                            : { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: post.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: post.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            seoPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);
            });

            it('should duplicate a simple pending post in the same restaurant as social post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .source(PostSource.SOCIAL)
                                        .text('Duplicate me please!')
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];

                        return {
                            socialPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: post.title,
                                        text: post.text ?? '',
                                        platformKeys: post.keys ?? [],
                                        published: post.published,
                                        isPublishing: false,
                                        postType: post.postType,
                                        location: post.location ?? null,
                                        callToAction: post.callToAction
                                            ? {
                                                  actionType: post.callToAction.actionType as MapstrCtaButtonType,
                                                  url: post.callToAction.url ?? '',
                                              }
                                            : undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: post.hashtags
                                            ? {
                                                  selected:
                                                      post.hashtags.selected?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                                  suggested:
                                                      post.hashtags.suggested?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                              }
                                            : { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: post.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: post.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            seoPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock.mock.calls).toEqual([
                    [
                        expect.any(Date),
                        AgendaJobName.PREPARE_POST,
                        {
                            userId: toDbId(author.id),
                            postId: toDbId(result.socialPostsDuplicated[0].post.id),
                        },
                    ],
                ]);
            });

            it('should duplicate a simple published post in the same restaurant as social post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();
                const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
                const tomorrowPlus15min = DateTime.now().plus({ days: 1, minutes: 15 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .source(PostSource.SOCIAL)
                                        .text('Duplicate me please!')
                                        .key(PlatformKey.INSTAGRAM)
                                        .keys([])
                                        .published(PostPublicationStatus.PUBLISHED)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];

                        return {
                            socialPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: post.title,
                                        text: post.text ?? '',
                                        platformKeys: [post.key!],
                                        published: PostPublicationStatus.DRAFT,
                                        isPublishing: false,
                                        postType: post.postType,
                                        location: post.location ?? null,
                                        callToAction: post.callToAction
                                            ? {
                                                  actionType: post.callToAction.actionType as MapstrCtaButtonType,
                                                  url: post.callToAction.url ?? '',
                                              }
                                            : undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: post.hashtags
                                            ? {
                                                  selected:
                                                      post.hashtags.selected?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                                  suggested:
                                                      post.hashtags.suggested?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                              }
                                            : { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: post.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: post.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            seoPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);

                const plannedPublicationDate = new Date(result.socialPostsDuplicated[0].post.plannedPublicationDate);
                expect(plannedPublicationDate.getTime()).toBeGreaterThan(tomorrow.getTime());
                expect(plannedPublicationDate.getTime()).toBeLessThanOrEqual(tomorrowPlus15min.getTime());
            });

            it('should duplicate multiple posts in the same restaurant as social post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();
                const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
                const tomorrowPlus15min = DateTime.now().plus({ days: 1, minutes: 15 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .source(PostSource.SOCIAL)
                                        .text("I'm draft, duplicate me please!")
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                    getDefaultPost()
                                        .source(PostSource.SOCIAL)
                                        .text("I'm pending, duplicate me please!")
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                    getDefaultPost()
                                        .source(PostSource.SOCIAL)
                                        .text("I'm published, duplicate me please!")
                                        .key(PlatformKey.INSTAGRAM)
                                        .keys([])
                                        .published(PostPublicationStatus.PUBLISHED)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(null)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const draftPost = dependencies.posts[0];
                        const pendingPost = dependencies.posts[1];
                        const publishedPost = dependencies.posts[2];

                        return {
                            socialPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: draftPost.title,
                                        text: draftPost.text ?? '',
                                        platformKeys: draftPost.keys ?? [],
                                        published: draftPost.published,
                                        isPublishing: false,
                                        postType: draftPost.postType,
                                        location: draftPost.location ?? null,
                                        callToAction: draftPost.callToAction
                                            ? {
                                                  actionType: draftPost.callToAction.actionType as MapstrCtaButtonType,
                                                  url: draftPost.callToAction.url ?? '',
                                              }
                                            : null,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: draftPost.hashtags
                                            ? {
                                                  selected:
                                                      draftPost.hashtags.selected?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                                  suggested:
                                                      draftPost.hashtags.suggested?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                              }
                                            : { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: draftPost.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: draftPost.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: pendingPost.title,
                                        text: pendingPost.text ?? '',
                                        platformKeys: pendingPost.keys ?? [],
                                        published: pendingPost.published,
                                        isPublishing: false,
                                        postType: pendingPost.postType,
                                        location: pendingPost.location ?? null,
                                        callToAction: pendingPost.callToAction
                                            ? {
                                                  actionType: pendingPost.callToAction.actionType as MapstrCtaButtonType,
                                                  url: pendingPost.callToAction.url ?? '',
                                              }
                                            : null,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: pendingPost.hashtags
                                            ? {
                                                  selected:
                                                      pendingPost.hashtags.selected?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                                  suggested:
                                                      pendingPost.hashtags.suggested?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                              }
                                            : { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: pendingPost.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: pendingPost.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: publishedPost.title,
                                        text: publishedPost.text ?? '',
                                        platformKeys: [publishedPost.key!],
                                        published: PostPublicationStatus.DRAFT,
                                        isPublishing: false,
                                        postType: publishedPost.postType,
                                        location: publishedPost.location ?? null,
                                        callToAction: publishedPost.callToAction
                                            ? {
                                                  actionType: publishedPost.callToAction.actionType as MapstrCtaButtonType,
                                                  url: publishedPost.callToAction.url ?? '',
                                              }
                                            : null,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: publishedPost.hashtags
                                            ? {
                                                  selected:
                                                      publishedPost.hashtags.selected?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                                  suggested:
                                                      publishedPost.hashtags.suggested?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                              }
                                            : { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: publishedPost.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: publishedPost.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            seoPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const draftPostId = seededObjects.posts[0]._id.toString();
                const pendingPostId = seededObjects.posts[1]._id.toString();
                const publishedPostId = seededObjects.posts[2]._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [draftPostId, pendingPostId, publishedPostId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock.mock.calls).toEqual([
                    [
                        expect.any(Date),
                        AgendaJobName.PREPARE_POST,
                        {
                            userId: toDbId(author.id),
                            postId: toDbId(result.socialPostsDuplicated[1].post.id),
                        },
                    ],
                ]);

                const plannedPublicationDate = new Date(result.socialPostsDuplicated[2].post.plannedPublicationDate);
                expect(plannedPublicationDate.getTime()).toBeGreaterThan(tomorrow.getTime());
                expect(plannedPublicationDate.getTime()).toBeLessThanOrEqual(tomorrowPlus15min.getTime());
            });
        });

        describe('Duplicate social posts in same restaurant as SEO posts', () => {
            it('should duplicate a simple draft post in the same restaurant as SEO post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').website('http://www.my-website.com').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text('Duplicate me please!')
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];
                        const restaurant = dependencies.restaurants[0];

                        return {
                            seoPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: post.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: post.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            socialPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SEO,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);
            });

            it('should duplicate a simple pending post in the same restaurant as SEO post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').website('http://www.my-website.com').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text('Duplicate me please!')
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];
                        const restaurant = dependencies.restaurants[0];

                        return {
                            seoPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: post.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: post.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            socialPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SEO,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);
            });

            it('should duplicate a simple published post in the same restaurant as SEO post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();
                const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
                const tomorrowPlus15min = DateTime.now().plus({ days: 1, minutes: 15 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').website('http://www.my-website.com').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text('Duplicate me please!')
                                        .key(PlatformKey.INSTAGRAM)
                                        .keys([])
                                        .published(PostPublicationStatus.PUBLISHED)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];
                        const restaurant = dependencies.restaurants[0];

                        return {
                            seoPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: post.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: post.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            socialPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SEO,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);

                const plannedPublicationDate = new Date(result.seoPostsDuplicated[0].post.plannedPublicationDate);
                expect(plannedPublicationDate.getTime()).toBeGreaterThan(tomorrow.getTime());
                expect(plannedPublicationDate.getTime()).toBeLessThanOrEqual(tomorrowPlus15min.getTime());
            });

            it('should duplicate multiple posts in the same restaurant as SEO posts', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();
                const tomorrow = DateTime.now().plus({ days: 1 }).toJSDate();
                const tomorrowPlus15min = DateTime.now().plus({ days: 1, minutes: 15 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text("I'm draft, duplicate me please!")
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                    getDefaultPost()
                                        .text("I'm pending, duplicate me please!")
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                    getDefaultPost()
                                        .text("I'm published, duplicate me please!")
                                        .key(PlatformKey.INSTAGRAM)
                                        .keys([])
                                        .published(PostPublicationStatus.PUBLISHED)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(null)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const draftPost = dependencies.posts[0];
                        const pendingPost = dependencies.posts[1];
                        const publishedPost = dependencies.posts[2];
                        const restaurant = dependencies.restaurants[0];

                        return {
                            seoPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: draftPost.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: draftPost.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: pendingPost.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: pendingPost.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: publishedPost.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: publishedPost.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            socialPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const draftPostId = seededObjects.posts[0]._id.toString();
                const pendingPostId = seededObjects.posts[1]._id.toString();
                const publishedPostId = seededObjects.posts[2]._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [draftPostId, pendingPostId, publishedPostId],
                    postDestination: PostSource.SEO,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);

                const plannedPublicationDate = new Date(result.seoPostsDuplicated[2].post.plannedPublicationDate);
                expect(plannedPublicationDate.getTime()).toBeGreaterThan(tomorrow.getTime());
                expect(plannedPublicationDate.getTime()).toBeLessThanOrEqual(tomorrowPlus15min.getTime());
            });
        });

        describe('Duplicate SEO posts in same restaurant as SEO posts', () => {
            it('should duplicate a simple draft SEO post in the same restaurant as SEO post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').website('http://www.my-website.com').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text('Duplicate me please!')
                                        .key(PlatformKey.GMB)
                                        .keys([])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .source(PostSource.SEO)
                                        .postTopic(SeoPostTopic.STANDARD)
                                        .callToAction({
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: 'http://www.my-website.com',
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];
                        const restaurant = dependencies.restaurants[0];

                        return {
                            seoPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: post.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: post.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            socialPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SEO,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);
            });

            it('should duplicate a simple pending SEO post in the same restaurant as SEO post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').website('http://www.my-website.com').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text('Duplicate me please!')
                                        .key(PlatformKey.GMB)
                                        .keys([])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .source(PostSource.SEO)
                                        .postTopic(SeoPostTopic.STANDARD)
                                        .callToAction({
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: 'http://www.my-website.com',
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];
                        const restaurant = dependencies.restaurants[0];

                        return {
                            seoPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        text: post.text ?? '',
                                        key: PlatformKey.GMB,
                                        published: PostPublicationStatus.DRAFT,
                                        postType: post.postType,
                                        postTopic: SeoPostTopic.STANDARD,
                                        callToAction: {
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: restaurant.website ?? '',
                                        },
                                        event: undefined,
                                        offer: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        attachments: expect.any(Array),
                                        author,
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            socialPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SEO,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);
            });
        });

        describe('Duplicate SEO posts in same restaurant as social posts', () => {
            it('should duplicate a simple draft SEO post in the same restaurant as social post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text('Duplicate me please!')
                                        .key(PlatformKey.GMB)
                                        .keys([])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .source(PostSource.SEO)
                                        .postTopic(SeoPostTopic.STANDARD)
                                        .callToAction({
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: 'http://www.my-website.com',
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];

                        return {
                            socialPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: post.title,
                                        text: post.text ?? '',
                                        platformKeys: getSocialPlatformKeysWithPost(),
                                        published: PostPublicationStatus.DRAFT,
                                        isPublishing: false,
                                        postType: post.postType,
                                        location: null,
                                        callToAction: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            seoPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock).toHaveBeenCalledTimes(0);
            });

            it('should duplicate a simple pending SEO post in the same restaurant as social post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [getDefaultRestaurant().uniqueKey('1').build()];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .text('Duplicate me please!')
                                        .key(PlatformKey.GMB)
                                        .keys([])
                                        .published(PostPublicationStatus.PENDING)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .source(PostSource.SEO)
                                        .postTopic(SeoPostTopic.STANDARD)
                                        .callToAction({
                                            actionType: CallToActionType.LEARN_MORE,
                                            url: 'http://www.my-website.com',
                                        })
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];

                        return {
                            socialPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: post.title,
                                        text: post.text ?? '',
                                        platformKeys: getSocialPlatformKeysWithPost(),
                                        published: PostPublicationStatus.PENDING,
                                        isPublishing: false,
                                        postType: post.postType,
                                        location: null,
                                        callToAction: undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[0]._id.toString(),
                                },
                            ],
                            seoPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantId = seededObjects.restaurants[0]._id.toString();

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantId });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantId });

                const result = await useCase.execute({
                    restaurantIds: [toRestaurantId],
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock.mock.calls).toEqual([
                    [
                        expect.any(Date),
                        AgendaJobName.PREPARE_POST,
                        {
                            userId: toDbId(author.id),
                            postId: toDbId(result.socialPostsDuplicated[0].post.id),
                        },
                    ],
                ]);
            });
        });

        describe('Duplicate social posts in other restaurants as social posts', () => {
            it('should duplicate a simple draft post with custom fields in the given restaurants as social post', async () => {
                const author = { id: newDbId().toString(), name: 'John', lastname: 'Doe', picture: undefined };
                const in2days = DateTime.now().plus({ days: 2 }).toJSDate();
                const in10days = DateTime.now().plus({ days: 10 }).toJSDate();

                const firstRestaurantId = newDbId();

                const customFields: DuplicatePostV2BodyDto['customFields'] = [
                    {
                        restaurantId: firstRestaurantId.toString(),
                        text: 'This is a custom text alright!',
                        platformKeys: [PlatformKey.INSTAGRAM, PlatformKey.FACEBOOK],
                        published: PostPublicationStatus.PENDING,
                        plannedPublicationDate: in10days,
                        hashtags: {
                            selected: [
                                {
                                    id: newDbId().toString(),
                                    text: 'customHashtag1',
                                    isCustomerInput: false,
                                    isMain: false,
                                    type: HashtagType.DESCRIPTION,
                                },
                                {
                                    id: newDbId().toString(),
                                    text: 'customHashtag2',
                                    isCustomerInput: false,
                                    isMain: false,
                                    type: HashtagType.UNKNOWN,
                                },
                            ],
                            suggested: [
                                {
                                    id: newDbId().toString(),
                                    text: 'customSuggestedHashtag1',
                                    isCustomerInput: false,
                                    isMain: false,
                                    type: HashtagType.DESCRIPTION,
                                },
                                {
                                    id: newDbId().toString(),
                                    text: 'customSuggestedHashtag2',
                                    isCustomerInput: false,
                                    isMain: false,
                                    type: HashtagType.UNKNOWN,
                                },
                            ],
                        },
                        location: {
                            id: 'ChIJN1',
                            name: 'Custom Location',
                            link: 'https://maps.google.com/?q=Custom+Location',
                            location: {
                                latitude: 0,
                                longitude: 0,
                                city: 'Custom City',
                                country: 'Custom Country',
                                zip: '12345',
                                street: 'Custom Street',
                            },
                        },
                    },
                ];

                const testCase = new TestCaseBuilderV2<'posts' | 'restaurants'>({
                    seeds: {
                        restaurants: {
                            data() {
                                return [
                                    getDefaultRestaurant().uniqueKey('1').build(),
                                    getDefaultRestaurant()._id(firstRestaurantId).uniqueKey('2').build(),
                                    getDefaultRestaurant().uniqueKey('3').build(),
                                ];
                            },
                        },
                        posts: {
                            data(dependencies) {
                                return [
                                    getDefaultPost()
                                        .source(PostSource.SOCIAL)
                                        .text('Duplicate me please!')
                                        .key(undefined)
                                        .keys([PlatformKey.INSTAGRAM])
                                        .published(PostPublicationStatus.DRAFT)
                                        .restaurantId(dependencies.restaurants()[0]._id)
                                        .attachments([])
                                        .plannedPublicationDate(in2days)
                                        .build(),
                                ];
                            },
                        },
                    },
                    expectedResult(dependencies): DuplicatePostV2ResponseDto {
                        const post = dependencies.posts[0];
                        const restaurantCustomFields = customFields[0];

                        return {
                            socialPostsDuplicated: [
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: post.title,
                                        text: restaurantCustomFields.text ?? '',
                                        platformKeys: restaurantCustomFields.platformKeys ?? [],
                                        published: restaurantCustomFields.published!,
                                        isPublishing: false,
                                        postType: post.postType,
                                        location: restaurantCustomFields.location,
                                        callToAction: post.callToAction
                                            ? {
                                                  actionType: post.callToAction.actionType as MapstrCtaButtonType,
                                                  url: post.callToAction.url ?? '',
                                              }
                                            : undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: restaurantCustomFields.plannedPublicationDate!.toISOString(),
                                        hashtags: restaurantCustomFields.hashtags ?? { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: post.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: post.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[1]._id.toString(),
                                },
                                {
                                    post: {
                                        id: expect.any(String),
                                        title: post.title,
                                        text: post.text ?? '',
                                        platformKeys: post.keys ?? [],
                                        published: post.published,
                                        isPublishing: false,
                                        postType: post.postType,
                                        location: post.location ?? null,
                                        callToAction: post.callToAction
                                            ? {
                                                  actionType: post.callToAction.actionType as MapstrCtaButtonType,
                                                  url: post.callToAction.url ?? '',
                                              }
                                            : undefined,
                                        error: undefined,
                                        socialLink: undefined,
                                        socialCreatedAt: undefined,
                                        feedbacks: null,
                                        plannedPublicationDate: expect.any(String),
                                        hashtags: post.hashtags
                                            ? {
                                                  selected:
                                                      post.hashtags.selected?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                                  suggested:
                                                      post.hashtags.suggested?.map((hashtag) => ({
                                                          id: hashtag._id.toString(),
                                                          text: hashtag.text,
                                                          isCustomerInput: hashtag.isCustomerInput,
                                                          isMain: hashtag.isMain,
                                                          type: hashtag.type,
                                                      })) ?? [],
                                              }
                                            : { selected: [], suggested: [] },
                                        attachments: expect.any(Array),
                                        author,
                                        userTagsList: post.userTagsList ?? [],
                                        bindingId: expect.any(String),
                                        tiktokOptions: post.tiktokOptions ?? {
                                            privacyStatus: TiktokPrivacyStatus.PUBLIC_TO_EVERYONE,
                                            interactionAbility: {
                                                comment: false,
                                                duet: false,
                                                stitch: false,
                                            },
                                            contentDisclosureSettings: {
                                                isActivated: false,
                                                yourBrand: false,
                                                brandedContent: false,
                                            },
                                        },
                                        instagramCollaboratorsUsernames: [],
                                    },
                                    restaurantId: dependencies.restaurants[2]._id.toString(),
                                },
                            ],
                            seoPostsDuplicated: [],
                        };
                    },
                });

                await testCase.build();

                const seededObjects = testCase.getSeededObjects();
                const post = seededObjects.posts[0];
                const postId = post._id.toString();
                const fromRestaurantId = seededObjects.restaurants[0]._id.toString();
                const toRestaurantIds = [seededObjects.restaurants[1]._id.toString(), seededObjects.restaurants[2]._id.toString()];

                const deleteJobsMock = jest.fn();
                const scheduleMock = jest.fn();
                const agendaSingletonMock = { deleteJobs: deleteJobsMock, schedule: scheduleMock } as unknown as AgendaSingleton;
                container.registerInstance(AgendaSingleton, agendaSingletonMock);
                const useCase = container.resolve(DuplicatePostUseCase);

                const userRestaurantsAbilityBuilder = new AbilityBuilder(Ability);
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantIds[0] });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantIds[0] });
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantIds[1] });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantIds[1] });
                userRestaurantsAbilityBuilder.can(CaslAction.PUBLISH, CaslSubject.SOCIAL_POST, { restaurantId: toRestaurantIds[2] });
                userRestaurantsAbilityBuilder.can(CaslAction.CREATE, CaslSubject.POST, { restaurantId: toRestaurantIds[2] });

                const result = await useCase.execute({
                    restaurantIds: toRestaurantIds,
                    postIdsToDuplicate: [postId],
                    postDestination: PostSource.SOCIAL,
                    fromRestaurantId,
                    userRestaurantsAbility: userRestaurantsAbilityBuilder.build(),
                    author,
                    customFields,
                });

                const expectedResult = testCase.getExpectedResult();

                expect(result).toEqual(expectedResult);
                expect(scheduleMock.mock.calls).toEqual([
                    [
                        expect.any(Date),
                        AgendaJobName.PREPARE_POST,
                        {
                            userId: toDbId(author.id),
                            postId: toDbId(result.socialPostsDuplicated[0].post.id),
                        },
                    ],
                ]);
            });
        });
    });
});
