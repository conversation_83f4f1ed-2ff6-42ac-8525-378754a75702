import assert from 'assert';
import { DateTime } from 'luxon';
import { FilterQuery } from 'mongoose';
import { container, inject, singleton } from 'tsyringe';

import {
    DbId,
    IPlatform,
    IPopulatedPost,
    IPost,
    IPostWithAttachments,
    IPostWithAttachmentsAndThumbnail,
    IRestaurant,
    ISocialAttachment,
    newDbId,
    toDbId,
} from '@malou-io/package-models';
import {
    formatSocialNetworkPostText,
    getFileExtension,
    getFileFormatFromExtension,
    MalouErrorCode,
    MediaCategory,
    MediaType,
    PlatformKey,
    PostPublicationStatus,
    postsUpdateTexts,
    PostType,
    TimeInMilliseconds,
    waitFor,
} from '@malou-io/package-utils';

import { AgendaSingleton } from ':helpers/classes/agenda-singleton';
import { MalouError } from ':helpers/classes/malou-error';
import { AgendaJobName } from ':helpers/enums/agenda-job-name.enum';
import { PlatformInsightFiltersFacebook } from ':helpers/filters/platform-insight-filters-api-factory';
import { logger } from ':helpers/logger';
import { FacebookCredentialsRepository } from ':modules/credentials/platforms/facebook/facebook.repository';
import { FacebookApiTypes } from ':modules/credentials/platforms/facebook/facebook.types';
import * as facebookCredentialsUseCases from ':modules/credentials/platforms/facebook/facebook.use-cases';
import { isFbTimeoutError } from ':modules/credentials/platforms/facebook/facebook.use-cases';
import FeedbacksRepository from ':modules/feedbacks/feedback.repository';
import { Media } from ':modules/media/entities/media.entity';
import { MediasRepository } from ':modules/media/medias.repository';
import { MediaUploaderService } from ':modules/media/services/media-uploader/media-uploader.service';
import { CreatePostErrorNotificationProducer } from ':modules/notifications/queues/create-post-error-notification/create-post-error-notification.producer';
import { Platform } from ':modules/platforms/platforms.entity';
import PlatformsRepository from ':modules/platforms/platforms.repository';
import { MalouPostData, PlatformPostUseCases, StoryToPublish } from ':modules/posts/posts.interface';
import PostsRepository from ':modules/posts/posts.repository';
import { sendCompletePublishPost } from ':modules/posts/queues/publish-post/publish-post.producer';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import FacebookApiProvider from ':providers/meta/facebook/facebook-api-provider';
import { SlackChannel, SlackService } from ':services/slack.service';

import { FacebookPostMapper } from './facebook-post-mapper';
import { FacebookPostType, FbPostData, FbPostInsight } from './facebook-post.interface';
import { FacebookPublishReelUseCase } from './facebook-publish-reel.use-case';

@singleton()
export class FacebookPostsUseCases implements PlatformPostUseCases {
    constructor(
        private readonly _postsRepository: PostsRepository,
        private readonly _platformsRepository: PlatformsRepository,
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _feedbacksRepository: FeedbacksRepository,
        @inject(SlackService)
        private readonly _slackService: SlackService,
        private readonly _mediaUploaderService: MediaUploaderService,
        private readonly _mediasRepository: MediasRepository,
        private readonly _facebookPublishReelUseCase: FacebookPublishReelUseCase,
        private readonly _facebookCredentialsRepository: FacebookCredentialsRepository,
        private readonly _facebookApiProvider: FacebookApiProvider,
        private readonly _createPostErrorNotificationProducer: CreatePostErrorNotificationProducer
    ) {}

    async synchronize({ platform, recentPostsOnly }: { platform: Platform; recentPostsOnly: boolean }): Promise<MalouPostData[]> {
        const postMapper = new FacebookPostMapper();
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const credential = await this._facebookCredentialsRepository.getCredentialById(credentialId.toString());

        const pageId = platform.socialId;
        assert(pageId);
        const { access_token: pageAccessToken } = await this._facebookApiProvider.refreshPageAccessToken(
            pageId,
            credential.userAccessToken
        );

        assert(pageAccessToken);
        const posts: MalouPostData[] = [];
        const pagePostsRes = await this._facebookApiProvider.getPagePosts({
            credential,
            pageId,
            pageAccessToken,
            recentPostsOnly,
        });
        posts.push(
            ...pagePostsRes.posts?.data
                .filter((p) => p.status_type !== 'mobile_status_update' && p.status_type !== 'shared_story')
                .filter((p) => !p.story || !postsUpdateTexts.FACEBOOK.filter((updateRegExp) => p.story?.match(updateRegExp))?.length)
                .map((post) => postMapper.mapToMalouPost({ post, platform }))
        );

        const timeBoundaries: { startDate: Date; endDate: Date } | undefined = recentPostsOnly
            ? {
                  startDate: DateTime.now().minus({ days: 1 }).toJSDate(),
                  endDate: DateTime.now().toJSDate(),
              }
            : undefined;
        const reelRes = await this._facebookApiProvider.getReels(pageAccessToken, pageId, timeBoundaries);
        posts.push(
            ...reelRes.map((reel) =>
                postMapper.mapPublishedFbReelToMalouPost(reel, {
                    restaurantId: platform.restaurantId.toString(),
                    platformId: platform._id.toString(),
                })
            )
        );
        return posts;
    }

    mapPostsDataToMalou = (posts: FbPostData[], platform: Platform): MalouPostData[] => {
        const postMapper = new FacebookPostMapper();
        return posts?.map((post) => postMapper.mapToMalouPost({ post, platform }));
    };

    mapPostDataToMalou = (fbPostData: FbPostData, platform: Platform): MalouPostData => {
        const postMapper = new FacebookPostMapper();
        return postMapper.mapToMalouPost({ post: fbPostData, platform });
    };

    fetchPost = async ({ post }: { post: IPost }): Promise<MalouPostData> => {
        const postMapper = new FacebookPostMapper();
        assert(post.platformId, 'Missing platformId on post');
        const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                metadata: {
                    postId: post._id,
                    platformId: post.platformId,
                },
            });
        }
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(post.socialId, 'Missing socialId on post');
        assert(platform.socialId, 'Missing socialId on platform');
        const fbPost = await facebookCredentialsUseCases.getPost(credentialId, post.socialId, platform.socialId);
        return postMapper.mapToMalouPost({ post: fbPost, platform });
    };

    publish = async ({ post }: { post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments }): Promise<MalouPostData | void> => {
        assert(post.platformId, 'Missing platformId on post');
        const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());
        if (!platform) {
            const malouError = new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                message: 'Platform not found in Facebook posts use case',
                metadata: { platformId: post.platformId },
            });
            await this._sendAlertForPublishFbPost(
                post,
                new Error(malouError.message),
                malouError.malouErrorCode,
                `Platform ${post.platformId} not found`
            );
            throw malouError;
        }
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            const malouError = new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
            await this._sendAlertForPublishFbPost(
                post,
                new Error(malouError.message),
                MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND,
                `Credials for platform ${post.platformId} not found`
            );
            throw malouError;
        }

        const facebookPostKind = this._getFacebookPostType(post);

        if (!facebookPostKind) {
            const malouError = new MalouError(MalouErrorCode.UNKNOWN_FB_POST_TYPE, {
                message: 'Invalid media types in attachments for Facebook post',
                metadata: { mediaTypes: post.attachments.map((media) => media.type) },
            });
            await this._sendAlertForPublishFbPost(
                post,
                new Error(malouError.message),
                malouError.malouErrorCode,
                `Unknown Facebook post type for given attachments`
            );
            throw malouError;
        }

        const pageId = platform.socialId;
        assert(pageId);
        const urls: string[] = post.attachments.map((attachment) => attachment.urls.igFit ?? attachment.urls.original);
        let postSocialId: string | undefined;

        if (facebookPostKind === FacebookPostType.VIDEO) {
            const fbMediaId = await facebookCredentialsUseCases.uploadPublishedVideo(credentialId, pageId, urls[0], post.text ?? '');
            // Although this is not the real fb post id, this id work (for ex: to get the post, lately in the complete publish step)
            // Not in the official documentation !!!
            postSocialId = `${platform.socialId}_${fbMediaId}`;
        } else if (facebookPostKind === FacebookPostType.PHOTOS) {
            const unpublishedFbPhotoIds: string[] = await Promise.all(
                urls.map((url) => facebookCredentialsUseCases.uploadUnpublishedPhoto(credentialId, pageId, url))
            );
            const fbPostId = await facebookCredentialsUseCases.publishPostWithUnpublishedFbPhotoIds(
                credentialId,
                pageId,
                {
                    text: post.text ?? '',
                    location: post.location?.id,
                },
                unpublishedFbPhotoIds
            );
            postSocialId = fbPostId;
        } else if (facebookPostKind === FacebookPostType.REEL) {
            await this._facebookPublishReelUseCase.execute(post, platform, credentialId);
            return;
        }
        await sendCompletePublishPost({
            postId: post._id,
            postSocialId,
        });
    };

    completePublish = async ({ postId, postSocialId }: { postId: DbId; postSocialId: string }): Promise<IPopulatedPost | undefined> => {
        logger.info('[COMPLETE_PUBLISH_FB_POST] - Started', { postId, postSocialId });
        const post = await this._postsRepository.findOne({
            filter: { _id: postId },
            options: { populate: [{ path: 'attachments' }] },
        });

        assert(post, 'Post not found');
        assert(post.platformId, 'Missing platformId on post');
        const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());
        try {
            const postMapper = new FacebookPostMapper();
            const credentialId = platform?.credentials?.[0];
            if (!credentialId) {
                logger.info('[COMPLETE_PUBLISH_FB_POST] - Ended', { error: MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND });
                throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
            }

            let fbPost: FbPostData | undefined;
            const maxTries = 5;
            for (let i = 0; i < maxTries; i++) {
                logger.info(`[COMPLETE_PUBLISH_FB_POST] - Started try n.${i + 1} to retrieve the fb post`);
                try {
                    assert(platform.socialId);
                    fbPost = await facebookCredentialsUseCases.getPost(credentialId, postSocialId, platform.socialId);
                    break;
                } catch (error: any) {
                    if (isFbTimeoutError(error)) {
                        if (i < maxTries - 1) {
                            continue;
                        }
                        if (i === maxTries - 1) {
                            logger.info(
                                '[COMPLETE_PUBLISH_FB_POST] - Fb timeout error on last try, schedule the post fetching and matching'
                            );
                            const agendaSingleton = container.resolve(AgendaSingleton);
                            const in5Minutes = DateTime.now().plus({ minutes: 5 }).toJSDate();

                            await agendaSingleton.schedule(in5Minutes, AgendaJobName.FETCH_AND_MATCH_FB_POST_TIMED_OUT, {
                                postId: toDbId(postId),
                            });
                        }
                    }
                    throw error;
                }
            }
            assert(fbPost, 'Post not found');
            logger.info(`[COMPLETE_PUBLISH_FB_POST] - Successfully retrieved fb post data`);
            const mappedPost = postMapper.mapToMalouPost({
                post: fbPost,
                platform,
            });

            // to avoid duplication in case post already inserted by webhook
            logger.info(
                // eslint-disable-next-line max-len
                `[COMPLETE_PUBLISH_FB_POST] - delete post with socialId: ${postSocialId} to avoid duplication in case post already inserted by webhook`
            );
            const alreadyExistingPostOrNull = await this._postsRepository.findOne({
                filter: { socialId: postSocialId, platformId: platform._id },
                options: { lean: true },
            });

            if (alreadyExistingPostOrNull) {
                await this._deletePostWithFeedback(alreadyExistingPostOrNull);
                logger.info(`[COMPLETE_PUBLISH_FB_POST] - post ${alreadyExistingPostOrNull._id} deleted`);
            } else {
                logger.info(`[COMPLETE_PUBLISH_FB_POST] - No post to delete`);
            }

            const updatedPost = await this._postsRepository.upsert({
                filter: { _id: post._id },
                update: mappedPost,
                options: {
                    lean: true,
                    populate: [{ path: 'attachments' }, { path: 'thumbnail' }, { path: 'feedback' }],
                },
            });

            logger.info(`[COMPLETE_PUBLISH_FB_POST] - Ended, post with id ${updatedPost._id} is updated`);

            return updatedPost;
        } catch (e: any) {
            if (e?.message?.match(/does not exist|n’est pas prêt/)) {
                // in case the error is just temporary reschedule the job
                if ((post.tries ?? 0) < 5) {
                    logger.warn('[COMPLETE_PUBLISH_FB_POST] [FB_MEDIA_NOT_READY] Retrying to publish media...', {
                        postSocialId,
                        postId: post._id,
                    });
                    post.tries = (post.tries || 0) + 1;
                    await this._postsRepository.updateOne({ filter: { _id: post._id }, update: { tries: post.tries } });
                    setTimeout(async () => {
                        await sendCompletePublishPost({
                            postId: post._id,
                            postSocialId,
                        });
                    }, TimeInMilliseconds.SECOND * 5);
                } else {
                    logger.warn('[COMPLETE_PUBLISH_FB_POST] [FB_MEDIA_NOT_READY]', e);
                    await this._sendAlertForPublishFbPost(
                        post,
                        e,
                        MalouErrorCode.MEDIA_NOT_READY_MAX_RETRIES_REACHED,
                        // eslint-disable-next-line max-len
                        `Max retries reached while trying to publish media on Facebook with error "does not exist" or "n'est pas prêt" for post ${post._id}`
                    );

                    throw new MalouError(MalouErrorCode.MEDIA_NOT_READY_MAX_RETRIES_REACHED, {
                        metadata: {
                            postId,
                            postSocialId,
                            platform: PlatformKey.FACEBOOK,
                        },
                    });
                }
            } else {
                logger.warn('[COMPLETE_PUBLISH_FB_POST] [ERROR_COMPLETE_PUBLISH_FB]', e);
                await this._sendAlertForPublishFbPost(
                    post,
                    e,
                    MalouErrorCode.COMPLETE_PUBLISH_POST_ERROR,
                    `Error while completing the publication of the post ${post._id} on Facebook`
                );
                throw e;
            }
        }
    };

    deletePost = async ({ post }: { post: IPost }) => {
        assert(post.platformId, 'Missing platformId on post');
        const platform = await this._platformsRepository.getPlatformById(post.platformId.toString());
        if (!platform) {
            throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND);
        }
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(post.socialId, 'Missing socialId on post');
        assert(platform.socialId, 'Missing socialId on platform');
        return facebookCredentialsUseCases.deletePagePost(credentialId, post.socialId, platform.socialId);
    };

    fetchPostsWithInsights = async (restaurantId: string, facebookFilters: PlatformInsightFiltersFacebook): Promise<FbPostInsight[]> => {
        const postsWithInsights = await this._fetchPostsWithInsights(restaurantId, facebookFilters);
        const reelsWithInsights = await this._fetchReelWithInsights(restaurantId, facebookFilters);
        return [...postsWithInsights, ...reelsWithInsights];
    };

    fetchPostsWithInsightsV2 = async (restaurantId: string, facebookFilters: PlatformInsightFiltersFacebook): Promise<FbPostInsight[]> => {
        const postsWithInsights = await this._fetchPostsWithInsights(restaurantId, facebookFilters);
        const reelsWithInsights = await this._fetchReelWithInsights(restaurantId, facebookFilters);
        return [...postsWithInsights, ...reelsWithInsights];
    };

    private _fetchPostsWithInsights = async (
        restaurantId: string,
        facebookFilters: PlatformInsightFiltersFacebook
    ): Promise<FbPostInsight[]> => {
        const { startDate, endDate } = facebookFilters;

        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.FACEBOOK);
        if (!platform?.credentials?.[0]) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        assert(platform.socialId, 'Missing socialId on platform');
        const posts = await facebookCredentialsUseCases.getPagePublishedPosts(
            platform.credentials[0],
            platform.socialId,
            startDate,
            endDate
        );
        const mapper = new FacebookPostMapper();
        // WARNING simple text posts are filtered (no attachments)
        return (
            posts
                // remove posts like an update status page or event
                .filter((p) => !!p.attachments?.data?.[0]?.media?.image?.src && p.attachments?.data[0]?.media_type !== 'event')
                .map((p) => mapper.mapToInsightPost(p))
        );
    };

    synchronizeStories = async (platform: Platform): Promise<Partial<IPost>[]> => {
        const credentialId = platform.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }
        const postMapper = new FacebookPostMapper();
        assert(platform.socialId, 'Missing socialId on platform');
        const fbStories = await facebookCredentialsUseCases.FbStories.getStories(credentialId.toString(), platform.socialId);
        if (!fbStories) {
            return [];
        }
        const pageAccessToken = await facebookCredentialsUseCases.getPageAccessTokenByCredentialIdAndSocialId(
            credentialId,
            platform.socialId
        );
        const fbStoriesWithMedia = await Promise.all(
            fbStories.map(async (story) => {
                const storyMedia =
                    story.media_type === FacebookApiTypes.Stories.StoryMediaType.PHOTO
                        ? await facebookCredentialsUseCases.FbStories.getStoryImage(credentialId, platform.socialId!, story.media_id)
                        : await facebookCredentialsUseCases.FbVideos.getVideoSource(pageAccessToken, story.media_id);
                return {
                    ...story,
                    media: storyMedia,
                };
            })
        );
        return fbStoriesWithMedia.map((story) => postMapper.mapToMalouStory(story, platform));
    };

    async publishStory(platformId: string, creationId: string, type: MediaType, postId: string): Promise<IPost> {
        try {
            const platform = await this._platformsRepository.getPlatformById(platformId);

            if (!platform) {
                throw new MalouError(MalouErrorCode.PLATFORM_NOT_FOUND, {
                    message: 'Platform not found in facebook posts use case',
                    metadata: { platformId },
                });
            }
            const credentialId = platform.credentials?.[0];
            const socialId = platform.socialId;
            if (!credentialId) {
                throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
            }

            assert(socialId, 'Missing socialId on platform');

            const result =
                type === MediaType.PHOTO
                    ? await this._publishAndGetPhotoStory(credentialId.toString(), socialId, creationId, platform)
                    : await this._publishAndGetVideoStory(credentialId.toString(), socialId, creationId, platform);

            return result;
        } catch (error: any) {
            logger.error('[PUBLISH_STORY_FACEBOOK_ERROR]', { postId, error });
            await this._sendAlertForPublishStory(postId, error);
            throw error;
        }
    }

    getMatchingPosts = async (post: IPost, fbPostsWithInsights: MalouPostData[]): Promise<MalouPostData[]> => {
        const formattedPostText = formatSocialNetworkPostText(post.text ?? '');
        const matchingPosts = fbPostsWithInsights.filter((fbPost) => fbPost.text === formattedPostText);
        return matchingPosts;
    };

    getMissingSocialPost = async (posts: MalouPostData[]): Promise<MalouPostData | undefined> => {
        for (const matchingPost of posts) {
            const found = await this._postsRepository.findOne({
                filter: { socialId: matchingPost.socialId, key: PlatformKey.FACEBOOK },
            });
            if (!found) {
                return matchingPost;
            }
        }

        return undefined;
    };

    buildDataToUpdateMatchedPost = ({
        socialId,
        socialAttachments,
        socialCreatedAt,
        socialUpdatedAt,
        postType,
        socialLink,
    }: {
        socialId: string;
        socialAttachments: ISocialAttachment[];
        socialCreatedAt: Date;
        socialUpdatedAt: Date;
        postType: PostType;
        socialLink: string;
    }) => ({
        socialId,
        socialAttachments,
        socialCreatedAt,
        socialUpdatedAt,
        postType,
        errorStage: null,
        errorData: null,
        published: PostPublicationStatus.PUBLISHED,
        socialLink,
    });

    updatePost = () => {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.FACEBOOK,
            },
        });
    };
    getCompetitorsPosts = () => {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.FACEBOOK,
            },
        });
    };

    async createStoryList(platform: IPlatform, posts: IPostWithAttachments[]): Promise<StoryToPublish[]> {
        const { credentials, socialId } = platform;
        const storyList: { plannedPublicationDate: Date; postId: DbId; creationId: string; type: MediaType }[] = [];
        const credentialId = credentials?.[0].toString();
        assert(credentialId, 'Missing credentialId on platform');
        assert(platform.socialId, 'Missing socialId on platform');
        assert(socialId, 'Missing socialId on platform');
        const pageAccessToken = await facebookCredentialsUseCases.getPageAccessTokenByCredentialIdAndSocialId(
            credentialId,
            platform.socialId
        );
        for (const post of posts) {
            const creationId = await this._createStoryWithSeveralTries(post, credentialId, pageAccessToken, socialId);
            assert(post.plannedPublicationDate, 'Post does not have a planned publication date');
            assert(creationId, 'Missing creationId');
            storyList.push({
                plannedPublicationDate: post.plannedPublicationDate,
                postId: post._id,
                creationId,
                type: post.attachments[0]?.type,
            });
        }
        return storyList;
    }

    async upsertStoryAndSaveAttachments(
        restaurantId: DbId,
        filter: FilterQuery<IPost>,
        update: Partial<IPost>
    ): Promise<IPostWithAttachments> {
        const currentStory = await this._postsRepository.findOne({ filter, options: { lean: true } });

        const mediaUrl = update.socialAttachments?.[0]?.urls?.original;
        const storyUpdate = { ...update };
        if (!currentStory?.attachments?.length && mediaUrl) {
            try {
                const uploadedMedia = await this._mediaUploaderService.uploadFromUrl(mediaUrl, restaurantId.toString());
                const mediaFormat = getFileFormatFromExtension(getFileExtension(mediaUrl));
                const id = newDbId().toString();
                const newMedia = await this._mediasRepository.createMedia(
                    new Media({
                        id,
                        socialId: id,
                        createdAt: new Date(),
                        updatedAt: new Date(),
                        ...uploadedMedia,
                        category: MediaCategory.ADDITIONAL,
                        format: mediaFormat,
                        type: Media.getMediaTypeFromFormat(mediaFormat),
                        restaurantId: restaurantId.toString(),
                        folderId: null,
                    })
                );
                storyUpdate.attachments = [toDbId(newMedia.id)];
            } catch (e) {
                logger.warn('[ERROR_UPSERT_STORY_AND_SAVE_ATTACHMENTS_FACEBOOK]', e);
            }
        }
        return this._postsRepository.upsert({
            filter,
            update: storyUpdate,
            options: {
                populate: [{ path: 'attachments' }],
            },
        });
    }

    searchAccounts = () => {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.FACEBOOK,
            },
        });
    };

    oembed = () => {
        throw new MalouError(MalouErrorCode.NOT_IMPLEMENTED, {
            metadata: {
                platform: PlatformKey.FACEBOOK,
            },
        });
    };

    private _fetchReelWithInsights = async (
        restaurantId: string,
        facebookFilters: PlatformInsightFiltersFacebook
    ): Promise<FbPostInsight[]> => {
        const platform = await this._platformsRepository.getPlatformByRestaurantIdAndPlatformKey(restaurantId, PlatformKey.FACEBOOK);
        const credentialId = platform?.credentials?.[0];
        if (!credentialId) {
            throw new MalouError(MalouErrorCode.PLATFORM_CREDENTIAL_NOT_FOUND);
        }

        const credential = await this._facebookCredentialsRepository.getCredentialById(credentialId);

        const pageId = platform.socialId;
        assert(pageId);
        const filters = {
            startDate: facebookFilters.startDate,
            endDate: facebookFilters.endDate,
        };
        const pageAccessToken = await facebookCredentialsUseCases.getPageAccessTokenBySocialId(credential, pageId);
        const reelsRes = await facebookCredentialsUseCases.FbReels.getReelsWithInsights(pageAccessToken, pageId, filters);
        const mapper = new FacebookPostMapper();
        return reelsRes.map((reel) => mapper.mapReelToInsightPost(reel));
    };

    private async _createStoryWithSeveralTries(
        post: IPostWithAttachments,
        credentialId: string,
        pageAccessToken: string,
        pageId: string
    ): Promise<string | undefined> {
        if (!post.attachments?.length) {
            throw new MalouError(MalouErrorCode.POST_MUST_HAVE_MEDIA, {
                message: 'No media attached to the post',
                metadata: { postId: post._id },
            });
        }

        const {
            type,
            urls: { original, igFit },
        } = post.attachments[0];

        const maxTries = 3;
        for (let i = 0; i < maxTries; i++) {
            let creationId: string;
            try {
                switch (type) {
                    case MediaType.PHOTO:
                        creationId = await facebookCredentialsUseCases.uploadUnpublishedPhoto(credentialId, pageId, igFit || original);
                        break;
                    case MediaType.VIDEO:
                        const { video_id: videoId, upload_url: uploadUrl } = await facebookCredentialsUseCases.FbStories.initUploadSession(
                            credentialId,
                            pageId
                        );
                        await facebookCredentialsUseCases.FbVideos.uploadVideo(pageAccessToken, uploadUrl, igFit || original);
                        creationId = videoId;
                        break;
                    default:
                        throw new MalouError(MalouErrorCode.CREDENTIALS_FACEBOOK_MEDIA_NOT_SUPPORTED, {
                            message: 'Media type not supported in facebook create story',
                            metadata: { type },
                        });
                }
                return creationId;
            } catch (error: any) {
                if (isFbTimeoutError(error) && i < maxTries - 1) {
                    await waitFor(5 * TimeInMilliseconds.SECOND);
                    continue;
                }
                logger.error('[COULD_NOT_CREATE_STORY_AFTER_RETRIES]', error);
                await this._sendAlertForPublishStory(post._id.toString(), error);
                throw new Error(error.message);
            }
        }
    }

    private async _publishAndGetPhotoStory(credentialId: string, socialId: string, creationId: string, platform: Platform): Promise<IPost> {
        const storySocialId = await this._publishPhotoStoryWithSeveralTries(credentialId, creationId, socialId);
        const stories = await facebookCredentialsUseCases.FbStories.getStories(credentialId, socialId);
        const story = stories?.find((s) => s.post_id === storySocialId);
        assert(story, 'Story not found');
        const storyMedia = await facebookCredentialsUseCases.FbStories.getStoryImage(credentialId, socialId, story.media_id);

        const postMapper = new FacebookPostMapper();
        return postMapper.mapToMalouStory({ ...story, media: storyMedia }, platform) as IPost;
    }

    private async _publishAndGetVideoStory(credentialId: string, socialId: string, creationId: string, platform: Platform): Promise<IPost> {
        const { success, message } = await this._waitForVideoStoryUpload(credentialId.toString(), socialId, creationId);
        if (!success) {
            throw new Error(message);
        }

        // trying to publish even if the video upload is not finished yet might trigger the processing phase of the upload
        const storySocialId = await this._publishVideoStoryWithSeveralTries(credentialId, creationId, socialId);

        const stories = await facebookCredentialsUseCases.FbStories.getStories(credentialId, socialId);
        const story = stories?.find((s) => s.post_id === storySocialId);
        assert(story, 'Story not found');
        const pageAccessToken = await facebookCredentialsUseCases.getPageAccessTokenByCredentialIdAndSocialId(credentialId, socialId);
        const storyMedia = await facebookCredentialsUseCases.FbVideos.getVideoSource(pageAccessToken, story?.media_id);
        const postMapper = new FacebookPostMapper();
        return postMapper.mapToMalouStory({ ...story, media: storyMedia }, platform) as IPost;
    }

    private async _waitForVideoStoryUpload(
        credentialId: string,
        socialId: string,
        storySocialId: string,
        isSecondRetryAfterPublishing = false
    ): Promise<{ success: boolean; message?: string }> {
        const maxTries = 20;
        for (let i = 0; i < maxTries; i++) {
            try {
                const pageAccessToken = await facebookCredentialsUseCases.getPageAccessTokenByCredentialIdAndSocialId(
                    credentialId,
                    socialId
                );
                const uploadStatus = await facebookCredentialsUseCases.FbVideos.getUploadStatus(pageAccessToken, storySocialId);
                if (uploadStatus?.status?.video_status === FacebookApiTypes.Videos.VideoStatus.READY) {
                    return { success: true };
                } else if (uploadStatus?.status?.video_status === FacebookApiTypes.Videos.VideoStatus.ERROR) {
                    const stepsDetails = Object.keys(uploadStatus?.status);
                    const keyWithErrors = stepsDetails?.find((stepKey) => uploadStatus?.status?.[stepKey]?.errors);
                    const errors: { code: string; message: string }[] = keyWithErrors
                        ? (uploadStatus?.status?.[keyWithErrors]?.errors ?? [])
                        : [];
                    const errorMessage = errors?.[0]?.message;
                    throw new Error(errorMessage);
                } else if (
                    !isSecondRetryAfterPublishing &&
                    [
                        FacebookApiTypes.Videos.VideoUploadPhaseStatus.COMPLETE,
                        FacebookApiTypes.Videos.VideoUploadPhaseStatus.IN_PROGRESS,
                    ].includes(uploadStatus?.status?.copyright_check_status?.status)
                ) {
                    return { success: true };
                }
                await waitFor(5 * TimeInMilliseconds.SECOND);
                continue;
            } catch (error: any) {
                if (isFbTimeoutError(error) && i < maxTries - 1) {
                    return { success: false };
                }
                logger.error('[COULD_NOT_UPLOAD_STORY_AFTER_RETRIES]', error);
                return { success: false, message: error.message };
            }
        }
        return { success: false };
    }

    private async _publishPhotoStoryWithSeveralTries(
        credentialId: string,
        creationId: string,
        pageId: string
    ): Promise<string | undefined> {
        const maxTries = 3;
        for (let i = 0; i < maxTries; i++) {
            try {
                const { success, post_id: storySocialId } = await facebookCredentialsUseCases.FbStories.publishPhotoStory(
                    credentialId,
                    pageId,
                    creationId
                );
                if (success) {
                    return storySocialId;
                }
                continue;
            } catch (error: any) {
                if (isFbTimeoutError(error) && i < maxTries - 1) {
                    await waitFor(5 * TimeInMilliseconds.SECOND);
                    continue;
                }
                logger.error('[COULD_NOT_PUBLISH_PHOTO_STORY_AFTER_RETRIES]', error);
                throw new Error(error.message);
            }
        }
    }

    private async _publishVideoStoryWithSeveralTries(
        credentialId: string,
        creationId: string,
        pageId: string
    ): Promise<string | undefined> {
        const maxTries = 3;
        for (let i = 0; i < maxTries; i++) {
            try {
                const {
                    success,
                    post_id: storySocialId,
                    message,
                } = await facebookCredentialsUseCases.FbStories.publishVideoStory(credentialId.toString(), pageId, creationId);
                // success is not a valid check because it can be true but with error message that upload is not finished yet
                if (message?.match(/Video is Processing/)) {
                    const { success: uploadSuccess, message: uploadMessage } = await this._waitForVideoStoryUpload(
                        credentialId.toString(),
                        pageId,
                        creationId,
                        true
                    );
                    if (!uploadSuccess) {
                        throw new Error(uploadMessage);
                    }
                    const stories = await facebookCredentialsUseCases.FbStories.getStories(credentialId, pageId);
                    const story = stories?.find((s) => s.post_id === storySocialId);
                    if (!story) {
                        continue;
                    }
                    return storySocialId;
                }
                if (success) {
                    return storySocialId;
                }
                continue;
            } catch (error: any) {
                if (isFbTimeoutError(error) && i < maxTries - 1) {
                    await waitFor(5 * TimeInMilliseconds.SECOND);
                    continue;
                }
                logger.error('[COULD_NOT_PUBLISH_VIDEO_STORY_AFTER_RETRIES]', error);
                throw error;
            }
        }
    }

    private _getFacebookPostType = (post: IPostWithAttachmentsAndThumbnail | IPostWithAttachments): FacebookPostType | null => {
        if (post.postType === PostType.REEL) {
            return FacebookPostType.REEL;
        }
        if (post.attachments.length === 1 && post.attachments[0].type === MediaType.VIDEO) {
            return FacebookPostType.VIDEO;
        }
        if (post.attachments.every((media) => media.type === MediaType.PHOTO)) {
            return FacebookPostType.PHOTOS;
        }
        return null;
    };

    private async _getRestaurantFromPost(post: { restaurantId: DbId }): Promise<IRestaurant | null> {
        const restaurant = await this._restaurantsRepository.findOne({ filter: { _id: post?.restaurantId } });
        return restaurant;
    }

    private async _sendAlertForPublishFbPost(
        post: { restaurantId: DbId; _id: DbId },
        e: Error,
        malouErrorCode: MalouErrorCode,
        description: string
    ): Promise<void> {
        const restaurantFromPost = await this._getRestaurantFromPost(post);
        const errorMessage = e.message ?? JSON.stringify(e);

        this._slackService.sendAlert({
            channel: SlackChannel.POSTS_V1_ALERTS,
            data: {
                err: new MalouError(malouErrorCode, { metadata: { rawError: errorMessage }, message: errorMessage }),
                endpoint: `restaurants/${restaurantFromPost?._id}/social/socialposts?postId=${post._id}`,
                metadata: { description, restaurantName: restaurantFromPost?.name ?? 'undefined' },
            },
        });
    }

    private async _sendAlertForPublishStory(postId: string, error: Error | MalouError): Promise<void> {
        const errorMessage = error.message ?? JSON.stringify(error);
        const malouErrorCode =
            (this._isMalouError(error) ? error.malouErrorCode : undefined) ?? MalouErrorCode.COMPLETE_PUBLISH_STORY_ERROR;

        const post = await this._postsRepository.findById(postId);
        assert(post, 'Post not found');
        const restaurantFromPost = await this._getRestaurantFromPost(post);
        await this._createPostErrorNotificationProducer.execute({ postId: post._id.toString() });

        this._slackService.sendAlert({
            channel: SlackChannel.POSTS_V1_ALERTS,
            data: {
                err: new MalouError(malouErrorCode, {
                    metadata: { rawError: errorMessage, platformKey: PlatformKey.FACEBOOK },
                    message: errorMessage,
                }),
                endpoint: `restaurants/${restaurantFromPost?._id}/social/socialposts?postId=${post._id}`,
                metadata: { description: 'Error while publishing story on Facebook', restaurantName: restaurantFromPost?.name ?? '' },
            },
        });
    }

    private async _deletePostWithFeedback(post: IPost) {
        // if a feedback is only linked to the post to delete, then delete the feedback too
        if (post.feedbackId) {
            const postsWithFeedback = await this._postsRepository.find({ filter: { feedbackId: post.feedbackId } });
            if (postsWithFeedback.length === 1) {
                await this._feedbacksRepository.deleteOne({ filter: { _id: post?.feedbackId } });
            }
        }
        await this._postsRepository.deleteOne({ filter: { _id: post._id } });
    }

    private _isMalouError(error: Error | MalouError): error is MalouError {
        return error instanceof MalouError && !!error.malouErrorCode;
    }
}
