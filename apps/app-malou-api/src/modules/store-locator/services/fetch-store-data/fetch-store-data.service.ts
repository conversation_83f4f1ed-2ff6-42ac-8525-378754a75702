import lodash from 'lodash';
import { singleton } from 'tsyringe';

import {
    GetStoreLocatorStorePageDto,
    storeLocatorStorePageCallToActionsBlockValidator,
    storeLocatorStorePageDescriptionsBlockValidator,
    storeLocatorStoreValidator,
} from '@malou-io/package-dto';
import { IStoreLocatorOrganizationConfigWithOrganization, IStoreLocatorRestaurantPage, toDbId } from '@malou-io/package-models';

import { logger } from ':helpers/logger';
import RestaurantsRepository from ':modules/restaurants/restaurants.repository';
import { FetchStoreLocatorGalleryBlockService } from ':modules/store-locator/services/fetch-store-gallery/fetch-store-gallery.service';
import { FetchStoreLocatorHeadBlockService } from ':modules/store-locator/services/fetch-store-head/fetch-store-head.service';
import { FetchStoreLocatorInformationBlockService } from ':modules/store-locator/services/fetch-store-information/fetch-store-information.service';
import { FetchStoreLocatorReviewsBlockService } from ':modules/store-locator/services/fetch-store-reviews/fetch-store-reviews.service';
import { FetchStoreLocatorSocialNetworksBlockService } from ':modules/store-locator/services/fetch-store-social-networks/fetch-store-social-networks.service';
import StoreLocatorRestaurantPageRepository from ':modules/store-locator/store-locator-restaurant-page.repository';

@singleton()
export class FetchStoreLocatorStoreService {
    constructor(
        private readonly _restaurantsRepository: RestaurantsRepository,
        private readonly _fetchStoreLocatorReviewsBlockService: FetchStoreLocatorReviewsBlockService,
        private readonly _fetchStoreLocatorInformationBlockService: FetchStoreLocatorInformationBlockService,
        private readonly _fetchStoreLocatorHeadBlockService: FetchStoreLocatorHeadBlockService,
        private readonly _fetchStoreLocatorSocialNetworksBlockService: FetchStoreLocatorSocialNetworksBlockService,
        private readonly _fetchStoreLocatorGalleryBlockService: FetchStoreLocatorGalleryBlockService,
        private readonly _storeLocatorRestaurantPageRepository: StoreLocatorRestaurantPageRepository
    ) {}

    async execute({
        restaurantId,
        storeLocatorOrganizationConfig,
        storeLocatorRestaurantPage,
    }: {
        restaurantId: string;
        storeLocatorOrganizationConfig: IStoreLocatorOrganizationConfigWithOrganization;
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; restaurantId: string; data: GetStoreLocatorStorePageDto | undefined }> {
        try {
            logger.info('[STORE_LOCATOR] Fetching store data');

            const restaurant = await this._restaurantsRepository.findOneOrFail({
                filter: { _id: toDbId(restaurantId) },
                options: {
                    lean: true,
                    populate: [
                        { path: 'category' },
                        { path: 'logo' },
                        { path: 'cover' },
                        { path: 'categoryList' },
                        { path: 'attributeList', populate: [{ path: 'attribute' }] },
                    ],
                },
            });

            const [headBlock, informationBlock, reviewsBlock, socialNetworksBlock, galleryBlock, callToActionsBlock, descriptionsBlock] =
                await Promise.all([
                    this._fetchStoreLocatorHeadBlockService.execute({
                        restaurant,
                        storeLocatorOrganizationConfig,
                        storeLocatorRestaurantPage,
                    }),
                    this._fetchStoreLocatorInformationBlockService.execute({
                        restaurantId,
                        storeLocatorRestaurantPage,
                    }),
                    this._fetchStoreLocatorReviewsBlockService.execute({ restaurantId, storeLocatorRestaurantPage }),
                    this._fetchStoreLocatorSocialNetworksBlockService.mapToSocialNetworksBlock({ storeLocatorRestaurantPage }),
                    this._fetchStoreLocatorGalleryBlockService.execute({ storeLocatorRestaurantPage }),
                    this._getCallToActionsBlock({ storeLocatorRestaurantPage }),
                    this._getDescriptionsBlock({ storeLocatorRestaurantPage }),
                ]);

            const storeData = {
                id: restaurant._id.toString(),
                name: restaurant.name,
                internalName: restaurant.internalName,
                organizationName: storeLocatorOrganizationConfig.organization.name,
                lang: storeLocatorRestaurantPage.lang,
                relativePath: storeLocatorRestaurantPage.relativePath,
                shouldDisplayWhiteMark: storeLocatorOrganizationConfig.shouldDisplayWhiteMark,
                styles: storeLocatorOrganizationConfig.styles.pages.store,
                headBlock: headBlock.data,
                informationBlock: informationBlock.data,
                galleryBlock: galleryBlock.data,
                reviewsBlock: reviewsBlock.data,
                socialNetworksBlock: socialNetworksBlock.data,
                descriptionsBlock: descriptionsBlock.data,
                callToActionsBlock: callToActionsBlock.data,
            };
            const success = lodash.every(
                [headBlock, informationBlock, reviewsBlock, socialNetworksBlock, galleryBlock, callToActionsBlock, descriptionsBlock],
                'success'
            );
            const parsedStoreData = await storeLocatorStoreValidator.parseAsync(storeData);

            return { success, restaurantId, data: parsedStoreData };
        } catch (err) {
            logger.error('[STORE_LOCATOR] Failed to fetch store data', { err });
            return { success: false, restaurantId, data: undefined };
        }
    }

    private async _getCallToActionsBlock({
        storeLocatorRestaurantPage,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['callToActionsBlock'] | undefined }> {
        try {
            const callToActionsBlock = {
                title: storeLocatorRestaurantPage.blocks.callToActions.title.toUpperCase(),
                links: storeLocatorRestaurantPage.blocks.callToActions.ctas,
            };
            const parsedCallToActionsBlock = await storeLocatorStorePageCallToActionsBlockValidator.parseAsync(callToActionsBlock);

            logger.info('[STORE_LOCATOR] [CallToActions block] CallToActions block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.callToActions.backup': parsedCallToActionsBlock },
            });

            return { success: true, data: parsedCallToActionsBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [CallToActions block] Failed to fetch store callToActions, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.callToActions?.backup) {
                try {
                    const callToActionsBlock = storeLocatorRestaurantPage.blocks.callToActions.backup;
                    const parsedCallToActionsBlock = await storeLocatorStorePageCallToActionsBlockValidator.parseAsync(callToActionsBlock);

                    return { success: false, data: parsedCallToActionsBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [CallToActions block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }

    private async _getDescriptionsBlock({
        storeLocatorRestaurantPage,
    }: {
        storeLocatorRestaurantPage: IStoreLocatorRestaurantPage;
    }): Promise<{ success: boolean; data: GetStoreLocatorStorePageDto['descriptionsBlock'] | undefined }> {
        try {
            const descriptionsBlock = {
                items: storeLocatorRestaurantPage.blocks.descriptions.items.map(({ title, image, blocks }) => ({
                    title: title.toUpperCase(),
                    imageUrl: image.url,
                    imageDescription: image.description,
                    blocks,
                })),
            };

            const parsedDescriptionsBlock = await storeLocatorStorePageDescriptionsBlockValidator.parseAsync(descriptionsBlock);

            logger.info('[STORE_LOCATOR] [Descriptions block] Descriptions block is valid, updating it as backup and returning it');
            await this._storeLocatorRestaurantPageRepository.updateOne({
                filter: { _id: storeLocatorRestaurantPage._id },
                update: { 'blocks.descriptions.backup': parsedDescriptionsBlock },
            });

            return { success: true, data: parsedDescriptionsBlock };
        } catch (err) {
            logger.error('[STORE_LOCATOR] [Descriptions block] Failed to fetch store descriptions, try to return backup', { err });

            if (storeLocatorRestaurantPage.blocks?.descriptions?.backup) {
                try {
                    const descriptionsBlock = storeLocatorRestaurantPage.blocks.descriptions.backup;
                    const parsedDescriptionsBlock = await storeLocatorStorePageDescriptionsBlockValidator.parseAsync(descriptionsBlock);

                    return { success: false, data: parsedDescriptionsBlock };
                } catch (error) {
                    logger.error('[STORE_LOCATOR] [Descriptions block] Failed to validate backup', { err: error });
                }
            }

            return { success: false, data: undefined };
        }
    }
}
