import { expect, it } from 'baseTest';

import { generateRandomString } from ':helpers';
import { openSeoPostModal } from ':shared/open-seo-post';

it('should duplicate post SEO to Social', async ({ page }) => {
    const randomText = `hello ${generateRandomString()}`;
    await page.goto(`/restaurants/${process.env.RESTAURANT_ID_LE_NINA}/seo/posts/list`);

    await openSeoPostModal(page);

    await page.getByTestId('seo-post-caption-input').fill(randomText);

    await page.getByTestId('draft-btn').click();

    await page.getByTestId('seo-duplicate-btn').click();

    await page.waitForResponse(/prepare/);

    await page.getByTestId('social-post-save-btn').click();

    await expect(await page.locator('app-malou-dialog')).toBeVisible();

    await page.getByTestId('malou-dialog-primary-btn').click();

    // wait for url to change
    await page.waitForURL(/social/i);

    const input = await page.getByTestId('social-post-caption-input').inputValue();

    expect(input).toContain(randomText);
});
